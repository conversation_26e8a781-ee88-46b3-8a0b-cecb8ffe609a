#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Componente Alert Panel per Agevolami PM
"""

import flet as ft
from typing import Callable, List, Dict, Any, Optional
from datetime import datetime, date

from core import get_logger
from core.models import Priority, AlertStatus

logger = get_logger(__name__)

class AlertPanel:
    """Pannello per visualizzare e gestire gli alert"""
    
    def __init__(self, app_instance, on_close: Callable[[], None]):
        self.app = app_instance
        self.on_close = on_close
        self.alerts_data = []
        self.filter_priority = None
        self.show_dismissed = False
        self.panel_container = None  # Reference to the main panel container
        
        self._init_components()
    
    def _init_components(self):
        """Inizializza i componenti del pannello"""
        pass
    
    def _create_header(self) -> ft.Container:
        """Crea l'header del pannello"""
        return ft.Container(
            content=ft.Row([
                ft.Column([
                    ft.Text(
                        "Alert Attivi",
                        size=18,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.GREY_800
                    ),
                    ft.Text(
                        self._get_header_count_text(),
                        size=12,
                        color=ft.Colors.GREY_500
                    )
                ], spacing=2),
                
                ft.Container(expand=True),
                
                ft.Row([
                    ft.IconButton(
                        icon=ft.Icons.REFRESH,
                        icon_size=20,
                        tooltip="Aggiorna",
                        on_click=lambda _: self.refresh_alerts()
                    ),
                    ft.IconButton(
                        icon=ft.Icons.CLOSE,
                        icon_size=20,
                        tooltip="Chiudi",
                        on_click=lambda _: self.on_close()
                    )
                ], spacing=0)
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            padding=ft.padding.all(16),
            border=ft.border.only(bottom=ft.BorderSide(1, ft.Colors.GREY_200))
        )
    
    def _create_filters(self) -> ft.Container:
        """Crea i filtri per gli alert"""
        
        # Filtro priorità
        priority_chips = []
        priorities = [
            ("Tutte", None, ft.Colors.GREY_600),
            ("Critica", Priority.CRITICAL, ft.Colors.RED_600),
            ("Alta", Priority.HIGH, ft.Colors.ORANGE_600),
            ("Media", Priority.MEDIUM, ft.Colors.BLUE_600),
            ("Bassa", Priority.LOW, ft.Colors.GREEN_600)
        ]
        
        for label, priority, color in priorities:
            is_selected = self.filter_priority == priority
            
            # Count alerts for this priority
            if priority is None:
                count = len(self.alerts_data)
            else:
                count = len([alert_data for alert_data in self.alerts_data if alert_data["alert"].priority == priority])
            
            chip = ft.Container(
                content=ft.Row([
                    ft.Text(
                        label,
                        size=11,
                        color=ft.Colors.WHITE if is_selected else color,
                        weight=ft.FontWeight.W_500
                    ),
                    ft.Container(
                        content=ft.Text(
                            str(count),
                            size=9,
                            color=ft.Colors.WHITE if is_selected else color,
                            weight=ft.FontWeight.BOLD
                        ),
                        bgcolor=ft.Colors.WHITE if is_selected else color,
                        padding=ft.padding.symmetric(horizontal=4, vertical=1),
                        border_radius=8,
                        visible=count > 0
                    )
                ], spacing=6, tight=True),
                padding=ft.padding.symmetric(horizontal=12, vertical=6),
                bgcolor=color if is_selected else ft.Colors.TRANSPARENT,
                border_radius=15,
                border=ft.border.all(1, color),
                on_click=lambda _, p=priority: self._set_priority_filter(p),
                animate=ft.Animation(150, ft.AnimationCurve.EASE_OUT)
            )
            priority_chips.append(chip)
        
        return ft.Container(
            content=ft.Column([
                ft.Text(
                    "Filtra per priorità:",
                    size=12,
                    color=ft.Colors.GREY_600,
                    weight=ft.FontWeight.W_500
                ),
                ft.Row(
                    controls=priority_chips,
                    spacing=8,
                    wrap=True
                )
            ], spacing=8),
            padding=ft.padding.all(16),
            border=ft.border.only(bottom=ft.BorderSide(1, ft.Colors.GREY_100))
        )
    
    def _set_priority_filter(self, priority: Optional[Priority]):
        """Imposta il filtro per priorità con feedback visivo"""
        try:
            self.filter_priority = priority
            
            # Count filtered alerts
            if priority is None:
                filtered_count = len(self.alerts_data)
                filter_name = "tutti gli alert"
            else:
                filtered_alerts = [alert_data for alert_data in self.alerts_data if alert_data["alert"].priority == priority]
                filtered_count = len(filtered_alerts)
                filter_name = f"alert con priorità {str(priority)}"
            
            # Update the panel content directly
            self._update_panel_content()
                
        except Exception as e:
            logger.error(f"Error setting priority filter: {e}")
            self._show_error(f"❌ Errore nell'applicazione del filtro: {e}")
    
    def _create_alert_item(self, alert_data: Dict[str, Any]) -> ft.Container:
        """Crea un elemento alert"""
        alert = alert_data["alert"]
        deadline = alert_data["deadline"]
        client = alert_data["client"]
        project = alert_data["project"]
        days_remaining = alert_data["days_remaining"]
        is_overdue = alert_data["is_overdue"]
        
        # Colori basati sulla priorità
        priority_colors = {
            Priority.CRITICAL: ft.Colors.RED_600,
            Priority.HIGH: ft.Colors.ORANGE_600,
            Priority.MEDIUM: ft.Colors.BLUE_600,
            Priority.LOW: ft.Colors.GREEN_600
        }
        
        priority_color = priority_colors.get(alert.priority, ft.Colors.GREY_600)
        
        # Icona basata sui giorni rimanenti
        if is_overdue:
            status_icon = ft.Icons.ERROR
            status_color = ft.Colors.RED_600
            status_text = "SCADUTO"
        elif days_remaining == 0:
            status_icon = ft.Icons.WARNING
            status_color = ft.Colors.ORANGE_600
            status_text = "OGGI"
        elif days_remaining <= 3:
            status_icon = ft.Icons.SCHEDULE
            status_color = ft.Colors.ORANGE_600
            status_text = f"{days_remaining}g"
        else:
            status_icon = ft.Icons.SCHEDULE
            status_color = ft.Colors.BLUE_600
            status_text = f"{days_remaining}g"
        
        # Informazioni aggiuntive
        subtitle_parts = []
        if client:
            subtitle_parts.append(client.name)
        if project:
            subtitle_parts.append(project.name)
        
        subtitle = " • ".join(subtitle_parts) if subtitle_parts else "Nessun progetto associato"
        
        return ft.Container(
            content=ft.Column([
                # Header dell'alert
                ft.Row([
                    # Indicatore priorità
                    ft.Container(
                        width=4,
                        height=40,
                        bgcolor=priority_color,
                        border_radius=2
                    ),
                    
                    # Contenuto principale
                    ft.Column([
                        ft.Row([
                            ft.Text(
                                alert.title,
                                size=13,
                                weight=ft.FontWeight.W_500,
                                color=ft.Colors.GREY_800,
                                expand=True
                            ),
                            
                            # Status badge
                            ft.Container(
                                content=ft.Row([
                                    ft.Icon(
                                        status_icon,
                                        size=12,
                                        color=status_color
                                    ),
                                    ft.Text(
                                        status_text,
                                        size=10,
                                        color=status_color,
                                        weight=ft.FontWeight.BOLD
                                    )
                                ], spacing=4),
                                padding=ft.padding.symmetric(horizontal=6, vertical=2),
                                bgcolor=ft.Colors.GREY_50,
                                border_radius=10,
                                border=ft.border.all(1, status_color)
                            )
                        ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                        
                        ft.Text(
                            subtitle,
                            size=11,
                            color=ft.Colors.GREY_500
                        ),
                        
                        ft.Text(
                            alert.message,
                            size=11,
                            color=ft.Colors.GREY_600,
                            max_lines=2
                        )
                    ], spacing=4, expand=True)
                ], spacing=12),
                
                # Azioni
                ft.Row([
                    ft.TextButton(
                        text="Visualizza",
                        icon=ft.Icons.VISIBILITY,
                        on_click=lambda _, a=alert: self._view_alert_details(a)
                    ),
                    
                    ft.TextButton(
                        text="Ignora",
                        icon=ft.Icons.CLOSE,
                        on_click=lambda _, a=alert: self._dismiss_alert(a)
                    ),
                    
                    ft.Container(expand=True),
                    
                    ft.Text(
                        alert.created_at.strftime("%d/%m %H:%M"),
                        size=10,
                        color=ft.Colors.GREY_400
                    )
                ], alignment=ft.MainAxisAlignment.START)
            ], spacing=8),
            padding=ft.padding.all(16),
            margin=ft.margin.symmetric(horizontal=8, vertical=4),
            bgcolor=ft.Colors.WHITE,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.GREY_200),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=2,
                color=ft.Colors.BLACK12
            )
        )
    
    def _view_alert_details(self, alert):
        """Visualizza i dettagli di un alert"""
        try:
            # Get full alert data
            alert_data = None
            for data in self.alerts_data:
                if data["alert"].id == alert.id:
                    alert_data = data
                    break
            
            if not alert_data:
                self._show_error("❌ Dati alert non trovati")
                return
            
            alert = alert_data["alert"]
            deadline = alert_data["deadline"]
            client = alert_data["client"]
            project = alert_data["project"]
            days_remaining = alert_data["days_remaining"]
            is_overdue = alert_data["is_overdue"]
            
            # Priority color mapping
            priority_colors = {
                Priority.CRITICAL: ft.Colors.RED_600,
                Priority.HIGH: ft.Colors.ORANGE_600,
                Priority.MEDIUM: ft.Colors.BLUE_600,
                Priority.LOW: ft.Colors.GREEN_600
            }
            priority_color = priority_colors.get(alert.priority, ft.Colors.GREY_600)
            
            # Status information
            if is_overdue:
                status_icon = ft.Icons.ERROR
                status_color = ft.Colors.RED_600
                status_text = "SCADUTO"
                status_bg = ft.Colors.RED_50
            elif days_remaining == 0:
                status_icon = ft.Icons.WARNING
                status_color = ft.Colors.ORANGE_600
                status_text = "SCADE OGGI"
                status_bg = ft.Colors.ORANGE_50
            elif days_remaining <= 3:
                status_icon = ft.Icons.SCHEDULE
                status_color = ft.Colors.ORANGE_600
                status_text = f"SCADE TRA {days_remaining} GIORNI"
                status_bg = ft.Colors.ORANGE_50
            else:
                status_icon = ft.Icons.SCHEDULE
                status_color = ft.Colors.BLUE_600
                status_text = f"SCADE TRA {days_remaining} GIORNI"
                status_bg = ft.Colors.BLUE_50
            
            def close_dialog(e):
                dialog.open = False
                self.app.page.update()
            
            def dismiss_from_dialog(e):
                """Dismiss alert from details dialog"""
                try:
                    self._show_loading("🗑️ Dismissing alert...")
                    success = self.app.alert_service.dismiss_alert(alert.id)
                    self._close_loading_feedback()
                    
                    if success:
                        dialog.open = False
                        self.refresh_alerts()
                        self._show_success("✅ Alert ignorato con successo")
                        self.app.page.update()
                    else:
                        self._show_error("❌ Errore durante l'eliminazione dell'alert")
                except Exception as ex:
                    logger.error(f"Error dismissing alert from dialog: {ex}")
                    self._close_loading_feedback()
                    self._show_error(f"❌ Errore: {ex}")
            
            def navigate_to_deadline(e):
                """Navigate to the related deadline"""
                try:
                    if deadline:
                        # Close dialog first
                        dialog.open = False
                        self.app.page.update()
                        
                        # Navigate to deadlines view
                        self.app.main_layout._navigate_to("deadlines")
                        self._show_success(f"📅 Navigazione a scadenza: {deadline.title}")
                    else:
                        self._show_info("ℹ️ Nessuna scadenza associata a questo alert")
                except Exception as ex:
                    logger.error(f"Error navigating to deadline: {ex}")
                    self._show_error(f"❌ Errore nella navigazione: {ex}")
            
            def navigate_to_project(e):
                """Navigate to the related project"""
                try:
                    if project:
                        # Close dialog first
                        dialog.open = False
                        self.app.page.update()
                        
                        # Navigate to projects view
                        self.app.main_layout._navigate_to("projects")
                        self._show_success(f"📁 Navigazione a progetto: {project.name}")
                    else:
                        self._show_info("ℹ️ Nessun progetto associato a questo alert")
                except Exception as ex:
                    logger.error(f"Error navigating to project: {ex}")
                    self._show_error(f"❌ Errore nella navigazione: {ex}")
            
            # Create content sections
            content_sections = []
            
            # Header with status
            content_sections.append(
                ft.Container(
                    content=ft.Column([
                        ft.Row([
                            ft.Container(
                                width=4,
                                height=30,
                                bgcolor=priority_color,
                                border_radius=2
                            ),
                            ft.Text(
                                alert.title,
                                size=18,
                                weight=ft.FontWeight.BOLD,
                                color=ft.Colors.GREY_800,
                                expand=True,
                                selectable=True
                            )
                        ], spacing=12),
                        
                        ft.Container(
                            content=ft.Row([
                                ft.Icon(status_icon, size=16, color=status_color),
                                ft.Text(
                                    status_text,
                                    size=14,
                                    color=status_color,
                                    weight=ft.FontWeight.BOLD
                                )
                            ], spacing=8),
                            bgcolor=status_bg,
                            padding=ft.padding.symmetric(horizontal=12, vertical=8),
                            border_radius=8,
                            border=ft.border.all(1, status_color)
                        )
                    ], spacing=12),
                    padding=ft.padding.all(16),
                    margin=ft.margin.only(bottom=8)
                )
            )
            
            # Alert message
            content_sections.append(
                ft.Container(
                    content=ft.Column([
                        ft.Row([
                            ft.Icon(ft.Icons.MESSAGE, size=16, color=ft.Colors.BLUE_600),
                            ft.Text("Messaggio Alert", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_600)
                        ], spacing=6),
                        ft.Text(
                            alert.message,
                            size=13,
                            color=ft.Colors.GREY_700,
                            selectable=True
                        )
                    ], spacing=8),
                    padding=ft.padding.all(12),
                    bgcolor=ft.Colors.BLUE_50,
                    border_radius=8,
                    border=ft.border.all(1, ft.Colors.BLUE_200),
                    margin=ft.margin.only(bottom=8)
                )
            )
            
            # Related information
            if deadline:
                content_sections.append(
                    ft.Container(
                        content=ft.Column([
                            ft.Row([
                                ft.Icon(ft.Icons.EVENT, size=16, color=ft.Colors.GREEN_600),
                                ft.Text("Scadenza Associata", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN_600)
                            ], spacing=6),
                            ft.Text(f"Titolo: {deadline.title}", size=12, color=ft.Colors.GREY_700, selectable=True),
                            ft.Text(f"Data: {deadline.due_date.strftime('%d/%m/%Y')}", size=12, color=ft.Colors.GREY_700),
                            ft.Text(f"Tipo: {getattr(deadline, 'deadline_type', 'N/A')}", size=12, color=ft.Colors.GREY_700) if hasattr(deadline, 'deadline_type') and deadline.deadline_type else ft.Container()
                        ], spacing=4),
                        padding=ft.padding.all(12),
                        bgcolor=ft.Colors.GREEN_50,
                        border_radius=8,
                        border=ft.border.all(1, ft.Colors.GREEN_200),
                        margin=ft.margin.only(bottom=8)
                    )
                )
            
            if project:
                content_sections.append(
                    ft.Container(
                        content=ft.Column([
                            ft.Row([
                                ft.Icon(ft.Icons.FOLDER, size=16, color=ft.Colors.PURPLE_600),
                                ft.Text("Progetto Associato", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.PURPLE_600)
                            ], spacing=6),
                            ft.Text(f"Nome: {project.name}", size=12, color=ft.Colors.GREY_700, selectable=True),
                            ft.Text(f"Stato: {project.status}", size=12, color=ft.Colors.GREY_700) if project.status else ft.Container()
                        ], spacing=4),
                        padding=ft.padding.all(12),
                        bgcolor=ft.Colors.PURPLE_50,
                        border_radius=8,
                        border=ft.border.all(1, ft.Colors.PURPLE_200),
                        margin=ft.margin.only(bottom=8)
                    )
                )
            
            if client:
                content_sections.append(
                    ft.Container(
                        content=ft.Column([
                            ft.Row([
                                ft.Icon(ft.Icons.BUSINESS, size=16, color=ft.Colors.ORANGE_600),
                                ft.Text("Cliente", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.ORANGE_600)
                            ], spacing=6),
                            ft.Text(f"Nome: {client.name}", size=12, color=ft.Colors.GREY_700, selectable=True),
                            ft.Text(f"Email: {client.email}", size=12, color=ft.Colors.GREY_700) if client.email else ft.Container()
                        ], spacing=4),
                        padding=ft.padding.all(12),
                        bgcolor=ft.Colors.ORANGE_50,
                        border_radius=8,
                        border=ft.border.all(1, ft.Colors.ORANGE_200),
                        margin=ft.margin.only(bottom=8)
                    )
                )
            
            # Metadata
            content_sections.append(
                ft.Container(
                    content=ft.Column([
                        ft.Row([
                            ft.Icon(ft.Icons.INFO, size=16, color=ft.Colors.GREY_600),
                            ft.Text("Informazioni Alert", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_600)
                        ], spacing=6),
                        ft.Text(f"Priorità: {str(alert.priority).upper()}", size=12, color=ft.Colors.GREY_700),
                        ft.Text(f"Creato: {alert.created_at.strftime('%d/%m/%Y %H:%M')}", size=12, color=ft.Colors.GREY_700),
                        ft.Text(f"ID: {alert.id}", size=10, color=ft.Colors.GREY_500, selectable=True)
                    ], spacing=4),
                    padding=ft.padding.all(12),
                    bgcolor=ft.Colors.GREY_50,
                    border_radius=8,
                    border=ft.border.all(1, ft.Colors.GREY_200),
                    margin=ft.margin.only(bottom=8)
                )
            )
            
            # Create dialog
            dialog = ft.AlertDialog(
                title=ft.Row([
                    ft.Icon(ft.Icons.VISIBILITY, size=20, color=ft.Colors.BLUE_600),
                    ft.Text("Dettagli Alert", size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_800)
                ], spacing=8),
                content=ft.Container(
                    content=ft.Column(
                        content_sections,
                        spacing=0,
                        scroll=ft.ScrollMode.AUTO
                    ),
                    width=600,
                    height=500
                ),
                actions=[
                    ft.TextButton(
                        "Chiudi",
                        on_click=close_dialog,
                        style=ft.ButtonStyle(color=ft.Colors.GREY_600)
                    ),
                    ft.OutlinedButton(
                        content=ft.Row([
                            ft.Icon(ft.Icons.FOLDER_OPEN, size=16, color=ft.Colors.PURPLE_600),
                            ft.Text("Vai a Progetto", size=12)
                        ], spacing=4, tight=True),
                        on_click=navigate_to_project,
                        disabled=not project,
                        style=ft.ButtonStyle(
                            color=ft.Colors.PURPLE_600,
                            side=ft.BorderSide(1, ft.Colors.PURPLE_300)
                        )
                    ),
                    ft.OutlinedButton(
                        content=ft.Row([
                            ft.Icon(ft.Icons.EVENT, size=16, color=ft.Colors.GREEN_600),
                            ft.Text("Vai a Scadenza", size=12)
                        ], spacing=4, tight=True),
                        on_click=navigate_to_deadline,
                        disabled=not deadline,
                        style=ft.ButtonStyle(
                            color=ft.Colors.GREEN_600,
                            side=ft.BorderSide(1, ft.Colors.GREEN_300)
                        )
                    ),
                    ft.OutlinedButton(
                        content=ft.Row([
                            ft.Icon(ft.Icons.CLOSE, size=16, color=ft.Colors.RED_600),
                            ft.Text("Ignora", size=12)
                        ], spacing=4, tight=True),
                        on_click=dismiss_from_dialog,
                        style=ft.ButtonStyle(
                            color=ft.Colors.RED_600,
                            side=ft.BorderSide(1, ft.Colors.RED_300)
                        )
                    )
                ],
                actions_alignment=ft.MainAxisAlignment.END
            )
            
            self.app.page.overlay.append(dialog)
            dialog.open = True
            self.app.page.update()
            
            logger.info(f"Visualizzazione dettagli alert: {alert.id}")
            
        except Exception as e:
            logger.error(f"Error showing alert details: {e}")
            self._show_error(f"❌ Errore nella visualizzazione: {e}")
    
    def _dismiss_alert(self, alert):
        """Ignora un alert con feedback visivo"""
        try:
            self._show_loading("🗑️ Dismissing alert...")
            success = self.app.alert_service.dismiss_alert(alert.id)
            self._close_loading_feedback()
            
            if success:
                # Refresh alerts data and update panel
                self.alerts_data = self.app.alert_service.get_dashboard_alerts(limit=50)
                self._update_panel_content()
                self._show_success("✅ Alert ignorato con successo")
                logger.info(f"Alert {alert.id} ignorato")
            else:
                self._show_error("❌ Errore durante l'eliminazione dell'alert")
        except Exception as e:
            logger.error(f"Errore dismissing alert: {e}")
            self._close_loading_feedback()
            self._show_error(f"❌ Errore: {e}")
    
    def _show_loading(self, message: str):
        """Show loading message with progress indicator"""
        try:
            loading_snack = ft.SnackBar(
                content=ft.Row([
                    ft.ProgressRing(width=20, height=20, stroke_width=3),
                    ft.Text(message, size=14, color=ft.Colors.WHITE)
                ], spacing=12),
                bgcolor=ft.Colors.BLUE_600,
                duration=5000,
                show_close_icon=True
            )
            
            if hasattr(self, 'app') and hasattr(self.app, 'page'):
                self.app.page.overlay.append(loading_snack)
                loading_snack.open = True
                self.app.page.update()
                
        except Exception as e:
            logger.error(f"Error showing loading feedback: {e}")
    
    def _show_success(self, message: str):
        """Show success message with visual feedback"""
        try:
            success_snack = ft.SnackBar(
                content=ft.Row([
                    ft.Icon(ft.Icons.CHECK_CIRCLE, color=ft.Colors.WHITE, size=20),
                    ft.Text(message, size=14, color=ft.Colors.WHITE)
                ], spacing=8),
                bgcolor=ft.Colors.GREEN_600,
                duration=4000,
                show_close_icon=True
            )
            
            if hasattr(self, 'app') and hasattr(self.app, 'page'):
                self.app.page.overlay.append(success_snack)
                success_snack.open = True
                self.app.page.update()
                
        except Exception as e:
            logger.error(f"Error showing success feedback: {e}")
    
    def _show_error(self, message: str):
        """Show error message with visual feedback"""
        try:
            error_snack = ft.SnackBar(
                content=ft.Row([
                    ft.Icon(ft.Icons.ERROR, color=ft.Colors.WHITE, size=20),
                    ft.Text(message, size=14, color=ft.Colors.WHITE)
                ], spacing=8),
                bgcolor=ft.Colors.RED_600,
                duration=6000,
                show_close_icon=True
            )
            
            if hasattr(self, 'app') and hasattr(self.app, 'page'):
                self.app.page.overlay.append(error_snack)
                error_snack.open = True
                self.app.page.update()
                
        except Exception as e:
            logger.error(f"Error showing error feedback: {e}")
    
    def _show_info(self, message: str):
        """Show info message with visual feedback"""
        try:
            info_snack = ft.SnackBar(
                content=ft.Row([
                    ft.Icon(ft.Icons.INFO, color=ft.Colors.WHITE, size=20),
                    ft.Text(message, size=14, color=ft.Colors.WHITE)
                ], spacing=8),
                bgcolor=ft.Colors.BLUE_500,
                duration=4000,
                show_close_icon=True
            )
            
            if hasattr(self, 'app') and hasattr(self.app, 'page'):
                self.app.page.overlay.append(info_snack)
                info_snack.open = True
                self.app.page.update()
                
        except Exception as e:
            logger.error(f"Error showing info feedback: {e}")
    
    def _close_loading_feedback(self):
        """Close any active loading feedback"""
        try:
            if hasattr(self, 'app') and hasattr(self.app, 'page'):
                overlays_to_remove = []
                for overlay in self.app.page.overlay:
                    if isinstance(overlay, ft.SnackBar) and overlay.open:
                        overlay.open = False
                        overlays_to_remove.append(overlay)
                
                for overlay in overlays_to_remove:
                    if overlay in self.app.page.overlay:
                        self.app.page.overlay.remove(overlay)
                
                self.app.page.update()
                
        except Exception as e:
            logger.error(f"Error closing loading feedback: {e}")
    
    def _create_alert_list(self) -> ft.Container:
        """Crea la lista degli alert"""
        if not self.alerts_data:
            return ft.Container(
                content=ft.Column([
                    ft.Icon(
                        ft.Icons.NOTIFICATIONS_OFF,
                        size=48,
                        color=ft.Colors.GREY_400
                    ),
                    ft.Text(
                        "Nessun alert attivo",
                        size=16,
                        color=ft.Colors.GREY_500,
                        weight=ft.FontWeight.W_500
                    ),
                    ft.Text(
                        "Tutti gli alert sono stati gestiti",
                        size=12,
                        color=ft.Colors.GREY_400
                    )
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=8),
                padding=ft.padding.all(40),
                alignment=ft.alignment.center
            )
        
        # Filtra gli alert
        filtered_alerts = self.alerts_data
        if self.filter_priority is not None:
            filtered_alerts = [
                alert_data for alert_data in filtered_alerts
                if alert_data["alert"].priority == self.filter_priority
            ]
        
        # Crea gli elementi
        alert_items = [self._create_alert_item(alert_data) for alert_data in filtered_alerts]
        
        return ft.Container(
            content=ft.Column(
                controls=alert_items,
                spacing=0,
                scroll=ft.ScrollMode.AUTO
            ),
            expand=True
        )
    
    def _update_alert_list(self):
        """Aggiorna la lista degli alert con filtri applicati"""
        try:
            # This method will be called to trigger a rebuild
            # The actual rebuild happens when the panel is recreated
            if hasattr(self, 'app') and hasattr(self.app, 'page'):
                self.app.page.update()
        except Exception as e:
            logger.error(f"Error updating alert list: {e}")
    
    def _update_panel_content(self):
        """Aggiorna il contenuto del pannello senza ricostruirlo completamente"""
        try:
            if hasattr(self, 'main_content') and self.main_content:
                # Update the main content with new components
                self.main_content.controls = [
                    # Header (updated with new count)
                    self._create_header(),
                    
                    # Filtri (updated with current selection)
                    self._create_filters(),
                    
                    # Lista alert (filtered)
                    self._create_alert_list()
                ]
                
                # Update the page
                if hasattr(self, 'app') and hasattr(self.app, 'page'):
                    self.app.page.update()
                    
        except Exception as e:
            logger.error(f"Error updating panel content: {e}")
            # Fallback to page update
            if hasattr(self, 'app') and hasattr(self.app, 'page'):
                self.app.page.update()
    
    def rebuild_panel(self):
        """Ricostruisce completamente il pannello alert"""
        try:
            if hasattr(self, 'app') and hasattr(self.app, 'page'):
                # Find and update the alert panel in overlays
                for i, overlay in enumerate(self.app.page.overlay):
                    if hasattr(overlay, 'content') and hasattr(overlay.content, 'controls'):
                        # Check if this looks like our alert panel
                        if (hasattr(overlay, 'width') and overlay.width == 400 and 
                            hasattr(overlay, 'bgcolor') and overlay.bgcolor == ft.Colors.WHITE):
                            # Replace with new panel
                            new_panel = self.build()
                            self.app.page.overlay[i] = new_panel
                            self.app.page.update()
                            return True
                return False
        except Exception as e:
            logger.error(f"Error rebuilding panel: {e}")
            return False
    
    def build(self) -> ft.Container:
        """Costruisce il pannello alert"""
        # Create the main content column
        self.main_content = ft.Column([
            # Header
            self._create_header(),
            
            # Filtri
            self._create_filters(),
            
            # Lista alert
            self._create_alert_list()
        ], spacing=0)
        
        # Create and store reference to the panel container
        self.panel_container = ft.Container(
            content=self.main_content,
            width=400,
            bgcolor=ft.Colors.WHITE,
            border_radius=ft.border_radius.only(
                top_left=12,
                bottom_left=12
            ),
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=10,
                color=ft.Colors.BLACK26
            )
        )
        
        return self.panel_container
    
    def refresh_alerts(self):
        """Aggiorna gli alert dal servizio"""
        try:
            self.alerts_data = self.app.alert_service.get_dashboard_alerts(limit=50)
            logger.info(f"Caricati {len(self.alerts_data)} alert")
            
            # Reset filter if no alerts match current filter
            if self.filter_priority is not None:
                filtered_alerts = [alert_data for alert_data in self.alerts_data if alert_data["alert"].priority == self.filter_priority]
                if not filtered_alerts:
                    self.filter_priority = None
            
            # Update the panel content
            self._update_panel_content()
                    
        except Exception as e:
            logger.error(f"Errore caricamento alert: {e}")
            self.alerts_data = []
    
    def _get_header_count_text(self) -> str:
        """Restituisce il testo del conteggio per l'header"""
        total_count = len(self.alerts_data)
        
        if self.filter_priority is None:
            return f"{total_count} alert trovati"
        else:
            filtered_count = len([alert_data for alert_data in self.alerts_data if alert_data["alert"].priority == self.filter_priority])
            return f"{filtered_count} di {total_count} alert ({str(self.filter_priority)})"
    
    def get_alert_count(self) -> int:
        """Restituisce il numero di alert attivi"""
        return len(self.alerts_data)