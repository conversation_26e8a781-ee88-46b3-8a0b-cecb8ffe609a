#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Unified Settings View for Agevolami PM
Clean, simple interface with all settings in one place
"""

import flet as ft
from typing import Dict, Any, Optional, List
import os
import json
from pathlib import Path

from core import get_logger, AppConfig
from core.services import EmailService, GoogleDriveService, GoogleTasksService
from core.services.google_calendar_service import GoogleCalendarService
from core.utils.windows_utils import WindowsSystemIntegration

logger = get_logger(__name__)

class SettingsView:
    """
    Unified Settings View - All settings in one clean interface
    """

    def __init__(self, app_instance):
        self.app = app_instance
        self.config = app_instance.config

        # Initialize services
        self.email_service = EmailService(self.config)
        self.windows_integration = WindowsSystemIntegration()
        self.google_drive_service = GoogleDriveService(self.config.data_dir)
        self.google_calendar_service = GoogleCalendarService(os.path.join(self.config.data_dir, "config"))
        self.google_tasks_service = GoogleTasksService(os.path.join(self.config.data_dir, "config"))

        # Settings file path
        self.settings_file = os.path.join(self.config.data_dir, "settings.json")

        # Load settings
        self.settings = self._load_settings()

        # Current section
        self.current_section = "email"

        # Store reference to main container for rebuilding
        self.main_container = None

        logger.info(f"[SETTINGS] Unified SettingsView initialized")

    def _load_settings(self) -> Dict[str, Any]:
        """Load settings from file"""
        default_settings = {
            "email": {
                "server": "",
                "port": 587,
                "username": "",
                "password": "",
                "use_tls": True,
                "sender_name": "Agevolami PM",
                "sender_email": ""
            },
            "notifications": {
                "enabled": True,
                "email_enabled": False,
                "check_interval": 24,
                "advance_days": 7
            },
            "google_services": {
                "drive_enabled": False,
                "backup_frequency": "weekly",
                "retention_days": 30,
                "tasks_enabled": False,
                "calendar_enabled": False,
                "calendar_auto_sync": False,
                "sync_completed": False
            },
            "reports": {
                "auto_enabled": False,
                "frequency": "weekly",
                "email_recipients": [],
                "scheduled_enabled": True,
                "morning_enabled": True,
                "morning_time": "08:00",
                "evening_enabled": True,
                "evening_time": "18:00",
                "workdays_only": True,
                "content_types": {
                    "project_completion": True,
                    "overdue_items": True,
                    "upcoming_deadlines": True,
                    "incomplete_tasks": True,
                    "client_summary": True,
                    "statistics": True
                },
                "recipients": [],
                "days_ahead_filter": 15,
                "include_project_percentages": True,
                "include_overdue_items": True,
                "include_upcoming_deadlines": True
            },
            "incentives": {
                "enabled": False,
                "frequency": "weekly",
                "keywords": ["incentivi", "finanziamenti", "bandi", "agevolazioni"],
                "openrouter_api_key": "",
                "llm_model": "deepseek/deepseek-r1-0528-qwen3-8b:free",
                "llm_api_url": "https://openrouter.ai/api/v1",
                "llm_enabled": False,
                "email_notifications": True,
                "notification_email": "",
                "custom_models": {},
                "websites": [
                    {
                        "name": "MIMIT - Ministero Imprese",
                        "url": "https://www.mimit.gov.it",
                        "enabled": True,
                        "search_paths": ["/it/incentivi-alle-imprese", "/it/notizie", "/it/bandi-e-avvisi"]
                    },
                    {
                        "name": "Invitalia",
                        "url": "https://www.invitalia.it",
                        "enabled": True,
                        "search_paths": ["/cosa-facciamo/sosteniamo-le-imprese", "/news-e-media/news", "/bandi-e-gare"]
                    },
                    {
                        "name": "SIMEST",
                        "url": "https://www.simest.it",
                        "enabled": True,
                        "search_paths": ["/servizi", "/news", "/bandi"]
                    }
                ]
            }
        }

        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    loaded_settings = json.load(f)
                    # Merge with defaults to ensure all keys exist
                    for category, defaults in default_settings.items():
                        if category not in loaded_settings:
                            loaded_settings[category] = defaults
                        else:
                            for key, default_value in defaults.items():
                                if key not in loaded_settings[category]:
                                    loaded_settings[category][key] = default_value
                    return loaded_settings
        except Exception as e:
            logger.error(f"Error loading settings: {e}")

        return default_settings
    def _save_settings(self) -> bool:
        """Save settings to file"""
        try:
            os.makedirs(os.path.dirname(self.settings_file), exist_ok=True)
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, indent=2, ensure_ascii=False)
            logger.info("Settings saved successfully")
            return True
        except Exception as e:
            logger.error(f"Error saving settings: {e}")
            return False

    def build(self) -> ft.Container:
        """Build the unified settings interface"""
        self.main_container = ft.Container(
            content=ft.Column([
                self._create_header(),
                ft.Row([
                    self._create_sidebar(),
                    self._create_content_area()
                ], expand=True)
            ], spacing=0),
            expand=True,
            bgcolor=ft.Colors.GREY_50
        )
        return self.main_container

    def _create_header(self) -> ft.Container:
        """Create the settings header"""
        def save_settings(e):
            if self._save_settings():
                self._show_notification("✅ Impostazioni salvate con successo!", ft.Colors.GREEN_600)
            else:
                self._show_notification("❌ Errore durante il salvataggio", ft.Colors.RED_600)

        return ft.Container(
            content=ft.Row([
                ft.Column([
                    ft.Text(
                        "⚙️ Impostazioni",
                        size=24,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.GREY_800
                    ),
                    ft.Text(
                        "Configura l'applicazione e i servizi",
                        size=14,
                        color=ft.Colors.GREY_600
                    )
                ], spacing=4),
                ft.Container(expand=True),
                ft.ElevatedButton(
                    text="Salva",
                    icon=ft.Icons.SAVE,
                    bgcolor=ft.Colors.BLUE_600,
                    color=ft.Colors.WHITE,
                    on_click=save_settings
                )
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            padding=ft.padding.all(20),
            bgcolor=ft.Colors.WHITE,
            border=ft.border.only(bottom=ft.BorderSide(1, ft.Colors.GREY_200))
        )
    def _create_sidebar(self) -> ft.Container:
        """Create the settings navigation sidebar"""
        sections = [
            {"key": "email", "title": "📧 Email", "icon": ft.Icons.EMAIL},
            {"key": "notifications", "title": "🔔 Notifiche", "icon": ft.Icons.NOTIFICATIONS},
            {"key": "google", "title": "🔗 Google Services", "icon": ft.Icons.CLOUD},
            {"key": "reports", "title": "📊 Report", "icon": ft.Icons.ANALYTICS},
            {"key": "incentives", "title": "💰 Incentivi", "icon": ft.Icons.TRENDING_UP},
        ]

        def on_section_click(section_key):
            def handler(e):
                self.current_section = section_key
                # Rebuild the entire settings view
                self._rebuild_view()
            return handler

        nav_items = []
        for section in sections:
            is_active = section["key"] == self.current_section
            nav_items.append(
                ft.Container(
                    content=ft.Row([
                        ft.Icon(section["icon"], size=20,
                               color=ft.Colors.WHITE if is_active else ft.Colors.GREY_600),
                        ft.Text(section["title"],
                               color=ft.Colors.WHITE if is_active else ft.Colors.GREY_700,
                               weight=ft.FontWeight.BOLD if is_active else ft.FontWeight.NORMAL)
                    ], spacing=12),
                    padding=ft.padding.all(12),
                    bgcolor=ft.Colors.BLUE_600 if is_active else ft.Colors.TRANSPARENT,
                    border_radius=8,
                    on_click=on_section_click(section["key"]),
                    animate=ft.Animation(200, ft.AnimationCurve.EASE_OUT)
                )
            )

        return ft.Container(
            content=ft.Column(nav_items, spacing=4),
            width=250,
            padding=ft.padding.all(16),
            bgcolor=ft.Colors.WHITE,
            border=ft.border.only(right=ft.BorderSide(1, ft.Colors.GREY_200))
        )

    def _create_content_area(self) -> ft.Container:
        """Create the main content area based on current section"""
        content_map = {
            "email": self._create_email_section,
            "notifications": self._create_notifications_section,
            "google": self._create_google_section,
            "reports": self._create_reports_section,
            "incentives": self._create_incentives_section
        }

        content_func = content_map.get(self.current_section, self._create_email_section)

        return ft.Container(
            content=ft.Column([
                content_func()
            ], spacing=16, scroll=ft.ScrollMode.AUTO),
            padding=ft.padding.all(20),
            expand=True,
            bgcolor=ft.Colors.GREY_50
        )

    def _create_email_section(self) -> ft.Container:
        """Create email settings section"""
        email_settings = self.settings["email"]

        def update_setting(key, value):
            self.settings["email"][key] = value

        return ft.Container(
            content=ft.Column([
                ft.Text("📧 Configurazione Email", size=20, weight=ft.FontWeight.BOLD),
                ft.Divider(),

                ft.TextField(
                    label="Server SMTP",
                    value=email_settings.get("server", ""),
                    on_change=lambda e: update_setting("server", e.control.value)
                ),

                ft.Row([
                    ft.TextField(
                        label="Porta",
                        value=str(email_settings.get("port", 587)),
                        width=100,
                        on_change=lambda e: update_setting("port", int(e.control.value) if e.control.value.isdigit() else 587)
                    ),
                    ft.Switch(
                        label="Usa TLS",
                        value=email_settings.get("use_tls", True),
                        on_change=lambda e: update_setting("use_tls", e.control.value)
                    )
                ], spacing=20),

                ft.TextField(
                    label="Username",
                    value=email_settings.get("username", ""),
                    on_change=lambda e: update_setting("username", e.control.value)
                ),

                ft.TextField(
                    label="Password",
                    value=email_settings.get("password", ""),
                    password=True,
                    can_reveal_password=True,
                    on_change=lambda e: update_setting("password", e.control.value)
                ),

                ft.Row([
                    ft.TextField(
                        label="Nome Mittente",
                        value=email_settings.get("sender_name", "Agevolami PM"),
                        expand=True,
                        on_change=lambda e: update_setting("sender_name", e.control.value)
                    ),
                    ft.TextField(
                        label="Email Mittente",
                        value=email_settings.get("sender_email", ""),
                        expand=True,
                        on_change=lambda e: update_setting("sender_email", e.control.value)
                    )
                ], spacing=12)

            ], spacing=16),
            padding=ft.padding.all(20),
            bgcolor=ft.Colors.WHITE,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.GREY_200)
        )

    def _create_notifications_section(self) -> ft.Container:
        """Create notifications settings section"""
        notif_settings = self.settings["notifications"]

        def update_setting(key, value):
            self.settings["notifications"][key] = value

        return ft.Container(
            content=ft.Column([
                ft.Text("🔔 Configurazione Notifiche", size=20, weight=ft.FontWeight.BOLD),
                ft.Divider(),

                ft.Switch(
                    label="Abilita Notifiche",
                    value=notif_settings.get("enabled", True),
                    on_change=lambda e: update_setting("enabled", e.control.value)
                ),

                ft.Switch(
                    label="Notifiche Email",
                    value=notif_settings.get("email_enabled", False),
                    on_change=lambda e: update_setting("email_enabled", e.control.value)
                ),

                ft.TextField(
                    label="Intervallo Controllo (ore)",
                    value=str(notif_settings.get("check_interval", 24)),
                    width=200,
                    on_change=lambda e: update_setting("check_interval", int(e.control.value) if e.control.value.isdigit() else 24)
                ),

                ft.TextField(
                    label="Giorni Anticipo Scadenze",
                    value=str(notif_settings.get("advance_days", 7)),
                    width=200,
                    on_change=lambda e: update_setting("advance_days", int(e.control.value) if e.control.value.isdigit() else 7)
                )

            ], spacing=16),
            padding=ft.padding.all(20),
            bgcolor=ft.Colors.WHITE,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.GREY_200)
        )
    def _create_google_section(self) -> ft.Container:
        """Create Google services settings section"""
        google_settings = self.settings["google_services"]

        def update_setting(key, value):
            self.settings["google_services"][key] = value

        def authenticate_drive(e):
            try:
                self._show_notification("🔄 Connessione a Google Drive...", ft.Colors.BLUE_600)
                success = self.google_drive_service.authenticate()
                if success:
                    self._show_notification("✅ Google Drive connesso con successo!", ft.Colors.GREEN_600)
                    self._save_settings()
                    self._rebuild_view()  # Refresh to show new status
                else:
                    self._show_notification("❌ Autenticazione Google Drive fallita", ft.Colors.RED_600)
            except Exception as ex:
                self._show_notification(f"❌ Errore autenticazione: {str(ex)}", ft.Colors.RED_600)

        def disconnect_drive(e):
            try:
                self.google_drive_service.disconnect()
                self._show_notification("✅ Google Drive disconnesso", ft.Colors.ORANGE_600)
                self._save_settings()
                self._rebuild_view()  # Refresh to show new status
            except Exception as ex:
                self._show_notification(f"❌ Errore disconnessione: {str(ex)}", ft.Colors.RED_600)

        def authenticate_calendar(e):
            try:
                self._show_notification("🔄 Connessione a Google Calendar...", ft.Colors.BLUE_600)
                success = self.google_calendar_service.authenticate()
                if success:
                    self._show_notification("✅ Google Calendar connesso con successo!", ft.Colors.GREEN_600)
                    self._save_settings()
                    self._rebuild_view()  # Refresh to show new status
                else:
                    self._show_notification("❌ Autenticazione Google Calendar fallita", ft.Colors.RED_600)
            except Exception as ex:
                self._show_notification(f"❌ Errore autenticazione: {str(ex)}", ft.Colors.RED_600)

        def disconnect_calendar(e):
            try:
                self.google_calendar_service.disconnect()
                self._show_notification("✅ Google Calendar disconnesso", ft.Colors.ORANGE_600)
                self._save_settings()
                self._rebuild_view()  # Refresh to show new status
            except Exception as ex:
                self._show_notification(f"❌ Errore disconnessione: {str(ex)}", ft.Colors.RED_600)

        def authenticate_tasks(e):
            try:
                self._show_notification("🔄 Connessione a Google Tasks...", ft.Colors.BLUE_600)
                success = self.google_tasks_service.authenticate()
                if success:
                    self._show_notification("✅ Google Tasks connesso con successo!", ft.Colors.GREEN_600)
                    self._save_settings()
                    self._rebuild_view()  # Refresh to show new status
                else:
                    self._show_notification("❌ Autenticazione Google Tasks fallita", ft.Colors.RED_600)
            except Exception as ex:
                self._show_notification(f"❌ Errore autenticazione: {str(ex)}", ft.Colors.RED_600)

        def disconnect_tasks(e):
            try:
                self.google_tasks_service.disconnect()
                self._show_notification("✅ Google Tasks disconnesso", ft.Colors.ORANGE_600)
                self._save_settings()
                self._rebuild_view()  # Refresh to show new status
            except Exception as ex:
                self._show_notification(f"❌ Errore disconnessione: {str(ex)}", ft.Colors.RED_600)

        def test_drive_connection(e):
            try:
                # Test Google Drive connection
                result = self.google_drive_service.test_connection()
                if result:
                    self._show_notification("✅ Google Drive connesso correttamente!", ft.Colors.GREEN_600)
                else:
                    self._show_notification("❌ Errore connessione Google Drive", ft.Colors.RED_600)
            except Exception as ex:
                self._show_notification(f"❌ Errore: {str(ex)}", ft.Colors.RED_600)

        def test_calendar_connection(e):
            try:
                # Test Google Calendar connection
                result = self.google_calendar_service.test_connection()
                if result:
                    self._show_notification("✅ Google Calendar connesso correttamente!", ft.Colors.GREEN_600)
                else:
                    self._show_notification("❌ Errore connessione Google Calendar", ft.Colors.RED_600)
            except Exception as ex:
                self._show_notification(f"❌ Errore: {str(ex)}", ft.Colors.RED_600)

        def test_tasks_connection(e):
            try:
                # Test Google Tasks connection
                result = self.google_tasks_service.test_connection()
                if result:
                    self._show_notification("✅ Google Tasks connesso correttamente!", ft.Colors.GREEN_600)
                else:
                    self._show_notification("❌ Errore connessione Google Tasks", ft.Colors.RED_600)
            except Exception as ex:
                self._show_notification(f"❌ Errore: {str(ex)}", ft.Colors.RED_600)

        def manual_backup(e):
            try:
                self._show_notification("🔄 Avvio backup manuale...", ft.Colors.BLUE_600)

                # Get database path and data directory
                database_path = self.config.database_path
                data_dir = self.config.data_dir
                retention_days = self.settings["google_services"].get("retention_days", 30)

                result = self.google_drive_service.create_backup(database_path, data_dir, retention_days)
                if result:
                    self._show_notification("✅ Backup completato con successo!", ft.Colors.GREEN_600)
                else:
                    self._show_notification("❌ Errore durante il backup", ft.Colors.RED_600)
            except Exception as ex:
                self._show_notification(f"❌ Errore backup: {str(ex)}", ft.Colors.RED_600)

        def view_backups(e):
            try:
                self._show_notification("📁 Caricamento lista backup...", ft.Colors.BLUE_600)
                self._show_backups_dialog()
            except Exception as ex:
                self._show_notification(f"❌ Errore: {str(ex)}", ft.Colors.RED_600)

        def manual_sync_calendar(e):
            try:
                self._show_notification("🔄 Sincronizzazione calendario...", ft.Colors.BLUE_600)
                # Manual calendar sync logic here
                self._show_notification("✅ Calendario sincronizzato!", ft.Colors.GREEN_600)
            except Exception as ex:
                self._show_notification(f"❌ Errore sync: {str(ex)}", ft.Colors.RED_600)

        def manual_sync_tasks(e):
            try:
                self._show_notification("🔄 Sincronizzazione tasks...", ft.Colors.BLUE_600)
                # Manual tasks sync logic here
                self._show_notification("✅ Tasks sincronizzati!", ft.Colors.GREEN_600)
            except Exception as ex:
                self._show_notification(f"❌ Errore sync: {str(ex)}", ft.Colors.RED_600)

        return ft.Container(
            content=ft.Column([
                ft.Text("🔗 Servizi Google", size=20, weight=ft.FontWeight.BOLD),
                ft.Divider(),

                # Compact 3-column layout
                ft.Row([
                    # Google Drive Column
                    ft.Container(
                        content=ft.Column([
                            ft.Row([
                                ft.Icon(ft.Icons.CLOUD, color=ft.Colors.BLUE_600, size=20),
                                ft.Text("Google Drive", size=14, weight=ft.FontWeight.BOLD),
                                # Authentication status indicator
                                ft.Icon(
                                    ft.Icons.CHECK_CIRCLE if self.google_drive_service.is_authenticated else ft.Icons.CANCEL,
                                    color=ft.Colors.GREEN_600 if self.google_drive_service.is_authenticated else ft.Colors.RED_600,
                                    size=16
                                )
                            ], spacing=6),

                            ft.Switch(
                                label="Abilita Backup",
                                value=google_settings.get("drive_enabled", False),
                                on_change=lambda e: update_setting("drive_enabled", e.control.value),
                                label_style=ft.TextStyle(size=12),
                                disabled=not self.google_drive_service.is_authenticated
                            ),

                            ft.Dropdown(
                                label="Frequenza",
                                value=google_settings.get("backup_frequency", "weekly"),
                                options=[
                                    ft.dropdown.Option("daily", "Giornaliero"),
                                    ft.dropdown.Option("weekly", "Settimanale"),
                                    ft.dropdown.Option("monthly", "Mensile")
                                ],
                                width=150,
                                text_size=12,
                                on_change=lambda e: update_setting("backup_frequency", e.control.value),
                                disabled=not self.google_drive_service.is_authenticated
                            ),

                            ft.Row([
                                # Authentication button (login/logout)
                                ft.IconButton(
                                    icon=ft.Icons.LOGOUT if self.google_drive_service.is_authenticated else ft.Icons.LOGIN,
                                    tooltip="Disconnetti" if self.google_drive_service.is_authenticated else "Connetti",
                                    on_click=disconnect_drive if self.google_drive_service.is_authenticated else authenticate_drive,
                                    bgcolor=ft.Colors.RED_600 if self.google_drive_service.is_authenticated else ft.Colors.GREEN_600,
                                    icon_color=ft.Colors.WHITE,
                                    icon_size=16
                                ),
                                ft.IconButton(
                                    icon=ft.Icons.WIFI_PROTECTED_SETUP,
                                    tooltip="Testa Connessione",
                                    on_click=test_drive_connection,
                                    bgcolor=ft.Colors.BLUE_600,
                                    icon_color=ft.Colors.WHITE,
                                    icon_size=16,
                                    disabled=not self.google_drive_service.is_authenticated
                                ),
                                ft.IconButton(
                                    icon=ft.Icons.BACKUP,
                                    tooltip="Backup Manuale",
                                    on_click=manual_backup,
                                    bgcolor=ft.Colors.GREEN_600,
                                    icon_color=ft.Colors.WHITE,
                                    icon_size=16,
                                    disabled=not self.google_drive_service.is_authenticated
                                ),
                                ft.IconButton(
                                    icon=ft.Icons.FOLDER,
                                    tooltip="Visualizza Backup",
                                    on_click=view_backups,
                                    bgcolor=ft.Colors.ORANGE_600,
                                    icon_color=ft.Colors.WHITE,
                                    icon_size=16,
                                    disabled=not self.google_drive_service.is_authenticated
                                )
                            ], spacing=4)
                        ], spacing=8),
                        padding=ft.padding.all(12),
                        bgcolor=ft.Colors.BLUE_50,
                        border_radius=8,
                        border=ft.border.all(1, ft.Colors.BLUE_200),
                        expand=True
                    ),

                    # Google Calendar Column
                    ft.Container(
                        content=ft.Column([
                            ft.Row([
                                ft.Icon(ft.Icons.CALENDAR_MONTH, color=ft.Colors.PURPLE_600, size=20),
                                ft.Text("Google Calendar", size=14, weight=ft.FontWeight.BOLD),
                                # Authentication status indicator
                                ft.Icon(
                                    ft.Icons.CHECK_CIRCLE if self.google_calendar_service.is_authenticated() else ft.Icons.CANCEL,
                                    color=ft.Colors.GREEN_600 if self.google_calendar_service.is_authenticated() else ft.Colors.RED_600,
                                    size=16
                                )
                            ], spacing=6),

                            ft.Switch(
                                label="Abilita Calendar",
                                value=google_settings.get("calendar_enabled", False),
                                on_change=lambda e: update_setting("calendar_enabled", e.control.value),
                                label_style=ft.TextStyle(size=12),
                                disabled=not self.google_calendar_service.is_authenticated()
                            ),

                            ft.Switch(
                                label="Sync Automatica",
                                value=google_settings.get("calendar_auto_sync", False),
                                on_change=lambda e: update_setting("calendar_auto_sync", e.control.value),
                                label_style=ft.TextStyle(size=12),
                                disabled=not self.google_calendar_service.is_authenticated()
                            ),

                            ft.Switch(
                                label="Sync Completate",
                                value=google_settings.get("sync_completed", False),
                                on_change=lambda e: update_setting("sync_completed", e.control.value),
                                label_style=ft.TextStyle(size=12),
                                disabled=not self.google_calendar_service.is_authenticated()
                            ),

                            ft.Row([
                                # Authentication button (login/logout)
                                ft.IconButton(
                                    icon=ft.Icons.LOGOUT if self.google_calendar_service.is_authenticated() else ft.Icons.LOGIN,
                                    tooltip="Disconnetti" if self.google_calendar_service.is_authenticated() else "Connetti",
                                    on_click=disconnect_calendar if self.google_calendar_service.is_authenticated() else authenticate_calendar,
                                    bgcolor=ft.Colors.RED_600 if self.google_calendar_service.is_authenticated() else ft.Colors.GREEN_600,
                                    icon_color=ft.Colors.WHITE,
                                    icon_size=16
                                ),
                                ft.IconButton(
                                    icon=ft.Icons.WIFI_PROTECTED_SETUP,
                                    tooltip="Testa Connessione",
                                    on_click=test_calendar_connection,
                                    bgcolor=ft.Colors.PURPLE_600,
                                    icon_color=ft.Colors.WHITE,
                                    icon_size=16,
                                    disabled=not self.google_calendar_service.is_authenticated()
                                ),
                                ft.IconButton(
                                    icon=ft.Icons.SYNC,
                                    tooltip="Sync Manuale",
                                    on_click=manual_sync_calendar,
                                    bgcolor=ft.Colors.GREEN_600,
                                    icon_color=ft.Colors.WHITE,
                                    icon_size=16,
                                    disabled=not self.google_calendar_service.is_authenticated()
                                )
                            ], spacing=4)
                        ], spacing=8),
                        padding=ft.padding.all(12),
                        bgcolor=ft.Colors.PURPLE_50,
                        border_radius=8,
                        border=ft.border.all(1, ft.Colors.PURPLE_200),
                        expand=True
                    ),

                    # Google Tasks Column
                    ft.Container(
                        content=ft.Column([
                            ft.Row([
                                ft.Icon(ft.Icons.TASK_ALT, color=ft.Colors.ORANGE_600, size=20),
                                ft.Text("Google Tasks", size=14, weight=ft.FontWeight.BOLD),
                                # Authentication status indicator
                                ft.Icon(
                                    ft.Icons.CHECK_CIRCLE if self.google_tasks_service.is_authenticated() else ft.Icons.CANCEL,
                                    color=ft.Colors.GREEN_600 if self.google_tasks_service.is_authenticated() else ft.Colors.RED_600,
                                    size=16
                                )
                            ], spacing=6),

                            ft.Switch(
                                label="Abilita Tasks",
                                value=google_settings.get("tasks_enabled", False),
                                on_change=lambda e: update_setting("tasks_enabled", e.control.value),
                                label_style=ft.TextStyle(size=12),
                                disabled=not self.google_tasks_service.is_authenticated()
                            ),

                            ft.Container(height=40),  # Spacer to align with other columns
                            ft.Container(height=40),  # Spacer to align with other columns

                            ft.Row([
                                # Authentication button (login/logout)
                                ft.IconButton(
                                    icon=ft.Icons.LOGOUT if self.google_tasks_service.is_authenticated() else ft.Icons.LOGIN,
                                    tooltip="Disconnetti" if self.google_tasks_service.is_authenticated() else "Connetti",
                                    on_click=disconnect_tasks if self.google_tasks_service.is_authenticated() else authenticate_tasks,
                                    bgcolor=ft.Colors.RED_600 if self.google_tasks_service.is_authenticated() else ft.Colors.GREEN_600,
                                    icon_color=ft.Colors.WHITE,
                                    icon_size=16
                                ),
                                ft.IconButton(
                                    icon=ft.Icons.WIFI_PROTECTED_SETUP,
                                    tooltip="Testa Connessione",
                                    on_click=test_tasks_connection,
                                    bgcolor=ft.Colors.ORANGE_600,
                                    icon_color=ft.Colors.WHITE,
                                    icon_size=16,
                                    disabled=not self.google_tasks_service.is_authenticated()
                                ),
                                ft.IconButton(
                                    icon=ft.Icons.SYNC,
                                    tooltip="Sync Manuale",
                                    on_click=manual_sync_tasks,
                                    bgcolor=ft.Colors.GREEN_600,
                                    icon_color=ft.Colors.WHITE,
                                    icon_size=16,
                                    disabled=not self.google_tasks_service.is_authenticated()
                                )
                            ], spacing=4)
                        ], spacing=8),
                        padding=ft.padding.all(12),
                        bgcolor=ft.Colors.ORANGE_50,
                        border_radius=8,
                        border=ft.border.all(1, ft.Colors.ORANGE_200),
                        expand=True
                    )
                ], spacing=12),

                ft.Container(height=12),

                # Compact Info Section
                ft.Container(
                    content=ft.Row([
                        ft.Icon(ft.Icons.INFO_OUTLINE, color=ft.Colors.BLUE_600, size=16),
                        ft.Text(
                            "I servizi Google richiedono autenticazione separata. Ogni servizio può essere configurato indipendentemente.",
                            size=12,
                            color=ft.Colors.GREY_700,
                            expand=True
                        )
                    ], spacing=8),
                    padding=ft.padding.all(12),
                    bgcolor=ft.Colors.BLUE_50,
                    border_radius=6,
                    border=ft.border.all(1, ft.Colors.BLUE_200)
                )

            ], spacing=16),
            padding=ft.padding.all(20),
            bgcolor=ft.Colors.WHITE,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.GREY_200)
        )
    def _create_reports_section(self) -> ft.Container:
        """Create reports settings section"""
        reports_settings = self.settings["reports"]

        def update_setting(key, value):
            self.settings["reports"][key] = value

        def update_content_type(key, value):
            if "content_types" not in self.settings["reports"]:
                self.settings["reports"]["content_types"] = {}
            self.settings["reports"]["content_types"][key] = value

        return ft.Container(
            content=ft.Column([
                ft.Text("📊 Configurazione Report", size=20, weight=ft.FontWeight.BOLD),
                ft.Divider(),

                # Basic settings
                ft.Row([
                    ft.Switch(
                        label="Report Automatici",
                        value=reports_settings.get("auto_enabled", False),
                        on_change=lambda e: update_setting("auto_enabled", e.control.value)
                    ),
                    ft.Switch(
                        label="Report Programmati",
                        value=reports_settings.get("scheduled_enabled", True),
                        on_change=lambda e: update_setting("scheduled_enabled", e.control.value)
                    )
                ], spacing=20),

                ft.Dropdown(
                    label="Frequenza",
                    value=reports_settings.get("frequency", "weekly"),
                    options=[
                        ft.dropdown.Option("daily", "Giornaliera"),
                        ft.dropdown.Option("weekly", "Settimanale"),
                        ft.dropdown.Option("monthly", "Mensile")
                    ],
                    on_change=lambda e: update_setting("frequency", e.control.value)
                ),

                # Schedule settings
                ft.Text("⏰ Orari Report", size=16, weight=ft.FontWeight.BOLD),
                ft.Row([
                    ft.Switch(
                        label="Report Mattutino",
                        value=reports_settings.get("morning_enabled", True),
                        on_change=lambda e: update_setting("morning_enabled", e.control.value)
                    ),
                    ft.TextField(
                        label="Orario Mattino",
                        value=reports_settings.get("morning_time", "08:00"),
                        width=120,
                        on_change=lambda e: update_setting("morning_time", e.control.value)
                    )
                ], spacing=20),

                ft.Row([
                    ft.Switch(
                        label="Report Serale",
                        value=reports_settings.get("evening_enabled", True),
                        on_change=lambda e: update_setting("evening_enabled", e.control.value)
                    ),
                    ft.TextField(
                        label="Orario Sera",
                        value=reports_settings.get("evening_time", "18:00"),
                        width=120,
                        on_change=lambda e: update_setting("evening_time", e.control.value)
                    )
                ], spacing=20),

                ft.Switch(
                    label="Solo Giorni Lavorativi",
                    value=reports_settings.get("workdays_only", True),
                    on_change=lambda e: update_setting("workdays_only", e.control.value)
                ),

                # Content types
                ft.Text("📋 Contenuti Report", size=16, weight=ft.FontWeight.BOLD),
                ft.Column([
                    ft.Switch(
                        label="Completamento Progetti",
                        value=reports_settings.get("content_types", {}).get("project_completion", True),
                        on_change=lambda e: update_content_type("project_completion", e.control.value)
                    ),
                    ft.Switch(
                        label="Elementi Scaduti",
                        value=reports_settings.get("content_types", {}).get("overdue_items", True),
                        on_change=lambda e: update_content_type("overdue_items", e.control.value)
                    ),
                    ft.Switch(
                        label="Scadenze Imminenti",
                        value=reports_settings.get("content_types", {}).get("upcoming_deadlines", True),
                        on_change=lambda e: update_content_type("upcoming_deadlines", e.control.value)
                    ),
                    ft.Switch(
                        label="Task Incompleti",
                        value=reports_settings.get("content_types", {}).get("incomplete_tasks", True),
                        on_change=lambda e: update_content_type("incomplete_tasks", e.control.value)
                    ),
                    ft.Switch(
                        label="Riepilogo Clienti",
                        value=reports_settings.get("content_types", {}).get("client_summary", True),
                        on_change=lambda e: update_content_type("client_summary", e.control.value)
                    ),
                    ft.Switch(
                        label="Statistiche",
                        value=reports_settings.get("content_types", {}).get("statistics", True),
                        on_change=lambda e: update_content_type("statistics", e.control.value)
                    )
                ], spacing=8),

                # Advanced settings
                ft.TextField(
                    label="Giorni Anticipo Filtro",
                    value=str(reports_settings.get("days_ahead_filter", 15)),
                    width=150,
                    on_change=lambda e: update_setting("days_ahead_filter", int(e.control.value) if e.control.value.isdigit() else 15)
                ),

                ft.TextField(
                    label="Email Destinatari (separati da virgola)",
                    value=", ".join(reports_settings.get("email_recipients", [])),
                    multiline=True,
                    min_lines=2,
                    max_lines=4,
                    on_change=lambda e: update_setting("email_recipients",
                                                     [email.strip() for email in e.control.value.split(",") if email.strip()])
                )

            ], spacing=16),
            padding=ft.padding.all(20),
            bgcolor=ft.Colors.WHITE,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.GREY_200)
        )

    def _create_incentives_section(self) -> ft.Container:
        """Create incentives monitoring settings section using new modular system"""
        try:
            # Import and use the new modular incentives section
            from ui.settings.sections.incentives_section import IncentivesSection
            
            # Create incentives section instance
            incentives_section = IncentivesSection(self.app)
            
            # Return the built section directly
            return incentives_section.build()
            
        except Exception as e:
            logger.error(f"Error creating incentives section with modular system: {e}")
            
            # Fallback to simplified version if modular system fails
            incentives_settings = self.settings.get("incentives", {})

            def update_setting(key, value):
                if "incentives" not in self.settings:
                    self.settings["incentives"] = {}
                self.settings["incentives"][key] = value
                self._save_settings()

            return ft.Container(
                content=ft.Column([
                    ft.Text("💰 Monitoraggio Incentivi", size=20, weight=ft.FontWeight.BOLD),
                    ft.Divider(),
                    
                    ft.Container(
                        content=ft.Column([
                            ft.Icon(ft.Icons.WARNING, size=48, color=ft.Colors.ORANGE_600),
                            ft.Text(
                                "Errore nel caricamento del sistema modular di configurazione",
                                size=16,
                                weight=ft.FontWeight.BOLD,
                                text_align=ft.TextAlign.CENTER
                            ),
                            ft.Text(
                                f"Errore: {str(e)}",
                                size=12,
                                color=ft.Colors.GREY_600,
                                text_align=ft.TextAlign.CENTER
                            ),
                            ft.ElevatedButton(
                                text="Usa Configurazione Legacy",
                                icon=ft.Icons.SETTINGS,
                                bgcolor=ft.Colors.ORANGE_600,
                                color=ft.Colors.WHITE,
                                on_click=lambda e: self._show_advanced_incentives_config()
                            )
                        ], spacing=12, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                        padding=ft.padding.all(20),
                        alignment=ft.alignment.center
                    )

                ], spacing=16),
                padding=ft.padding.all(20),
                bgcolor=ft.Colors.WHITE,
                border_radius=12,
                border=ft.border.all(1, ft.Colors.GREY_200)
            )

    def _show_advanced_incentives_config(self):
        """Show advanced incentives configuration dialog using new modular system"""
        try:
            # Import the new modular incentives section
            from ui.settings.sections.incentives_section import IncentivesSection

            # Create incentives section instance
            incentives_section = IncentivesSection(self.app)

            # Create and show dialog with the new section
            def close_dialog(e):
                self.app.page.close(incentives_dialog)

            incentives_dialog = ft.AlertDialog(
                modal=True,
                title=ft.Row([
                    ft.Icon(ft.Icons.TRENDING_UP, color=ft.Colors.PURPLE_600),
                    ft.Text("Configurazione Avanzata Incentivi", weight=ft.FontWeight.BOLD)
                ], spacing=8),
                content=ft.Container(
                    content=incentives_section.build(),
                    width=800,
                    height=600
                ),
                actions=[
                    ft.TextButton("Chiudi", on_click=close_dialog)
                ],
                actions_alignment=ft.MainAxisAlignment.END
            )

            self.app.page.open(incentives_dialog)

        except Exception as e:
            logger.error(f"Error showing advanced incentives config: {e}")
            # Fallback to old system if new system fails
            try:
                from ui.views.incentives_view import IncentivesView
                temp_incentives_view = IncentivesView(self.app)
                temp_incentives_view._show_configuration_dialog()
            except Exception as fallback_error:
                logger.error(f"Fallback also failed: {fallback_error}")
                self._show_notification(f"❌ Errore apertura configurazione avanzata: {str(e)}", ft.Colors.RED_600)

    def _show_notification(self, message: str, color: str):
        """Show notification to user"""
        try:
            # Try normal logging first
            logger.info(f"NOTIFICATION: {message}")
        except UnicodeEncodeError:
            # If Unicode error, log without special characters
            try:
                safe_message = message.encode('ascii', errors='replace').decode('ascii')
                logger.info(f"NOTIFICATION: {safe_message}")
            except:
                logger.info("NOTIFICATION: [message with special characters]")
        except Exception:
            # Skip logging if it fails
            pass

        try:
            if hasattr(self.app, 'page') and self.app.page:
                snack = ft.SnackBar(
                    content=ft.Text(message, color=ft.Colors.WHITE),
                    bgcolor=color,
                    duration=3000
                )
                self.app.page.overlay.append(snack)
                snack.open = True
                self.app.page.update()
        except Exception as e:
            # Skip error logging if it fails
            try:
                logger.error(f"Error showing notification: {e}")
            except:
                pass

    def refresh_data(self):
        """Refresh settings data - compatibility method"""
        self.settings = self._load_settings()
        logger.info("Settings data refreshed")

    def handle_setting_change(self, category: str, key: str, value):
        """Handle external setting changes - compatibility method"""
        if category in self.settings and key in self.settings[category]:
            self.settings[category][key] = value
            self._save_settings()
            logger.info(f"Setting updated: {category}.{key} = {value}")

    def _rebuild_view(self):
        """Rebuild the settings view when section changes"""
        if self.main_container and hasattr(self.app, 'page') and self.app.page:
            try:
                # Update the main container content
                self.main_container.content = ft.Column([
                    self._create_header(),
                    ft.Row([
                        self._create_sidebar(),
                        self._create_content_area()
                    ], expand=True)
                ], spacing=0)

                # Update the page
                self.app.page.update()

                # Safe logging
                try:
                    logger.info(f"Settings view rebuilt for section: {self.current_section}")
                except UnicodeEncodeError:
                    logger.info("Settings view rebuilt")
                except Exception:
                    pass

            except Exception as e:
                try:
                    logger.error(f"Error rebuilding settings view: {e}")
                except:
                    pass
                # Fallback to simple page update
                try:
                    self.app.page.update()
                except Exception:
                    pass

    def _show_backups_dialog(self):
        """Show dialog with list of available backups"""
        try:
            # Get list of backups from Google Drive
            backups = self.google_drive_service.list_backups()

            def close_dialog(e):
                """Properly close dialog and prevent grey screen"""
                try:
                    # Use the proper Flet dialog close method
                    self.app.page.close(dialog)
                    logger.info("Backup dialog closed successfully")
                except Exception as ex:
                    logger.error(f"Error closing backup dialog: {ex}")
                    # Force close as fallback
                    try:
                        dialog.open = False
                        self.app.page.update()
                    except:
                        pass

            def delete_backup(backup_id: str, backup_name: str):
                """Delete a specific backup"""
                def confirm_delete(e):
                    try:
                        # Close confirmation dialog first
                        self.app.page.close(confirm_dialog)

                        self._show_notification("🗑️ Eliminazione backup...", ft.Colors.ORANGE_600)

                        # Delete from Google Drive
                        success = self.google_drive_service.delete_backup(backup_id)

                        if success:
                            self._show_notification("✅ Backup eliminato!", ft.Colors.GREEN_600)
                            # Refresh backup list
                            self._show_backups_dialog()
                        else:
                            self._show_notification("❌ Errore eliminazione backup", ft.Colors.RED_600)

                    except Exception as ex:
                        self._show_notification(f"❌ Errore: {str(ex)}", ft.Colors.RED_600)

                def cancel_delete(e):
                    # Close confirmation dialog and show backup list again
                    self.app.page.close(confirm_dialog)
                    self._show_backups_dialog()

                # Show confirmation dialog
                confirm_dialog = ft.AlertDialog(
                    modal=True,
                    title=ft.Text("Conferma Eliminazione", weight=ft.FontWeight.BOLD),
                    content=ft.Text(f"Sei sicuro di voler eliminare il backup:\n{backup_name}?"),
                    actions=[
                        ft.TextButton("Annulla", on_click=cancel_delete),
                        ft.ElevatedButton(
                            "Elimina",
                            on_click=confirm_delete,
                            bgcolor=ft.Colors.RED_600,
                            color=ft.Colors.WHITE
                        )
                    ],
                    actions_alignment=ft.MainAxisAlignment.SPACE_BETWEEN
                )

                # Use proper dialog method
                self.app.page.open(confirm_dialog)

            def restore_backup(backup_id: str, backup_name: str):
                """Restore a specific backup"""
                def confirm_restore(e):
                    try:
                        # Close confirmation dialog first
                        self.app.page.close(confirm_dialog)

                        self._show_notification("🔄 Ripristino backup...", ft.Colors.BLUE_600)

                        # Create temp directory for restore
                        import tempfile
                        temp_dir = Path(tempfile.mkdtemp())

                        # Restore from Google Drive
                        success = self.google_drive_service.restore_backup(backup_id, temp_dir)

                        if success:
                            self._show_notification("✅ Backup ripristinato! Riavvia l'app.", ft.Colors.GREEN_600)
                        else:
                            self._show_notification("❌ Errore ripristino backup", ft.Colors.RED_600)

                    except Exception as ex:
                        self._show_notification(f"❌ Errore: {str(ex)}", ft.Colors.RED_600)

                def cancel_restore(e):
                    # Close confirmation dialog and show backup list again
                    self.app.page.close(confirm_dialog)
                    self._show_backups_dialog()

                # Show confirmation dialog
                confirm_dialog = ft.AlertDialog(
                    modal=True,
                    title=ft.Text("Conferma Ripristino", weight=ft.FontWeight.BOLD),
                    content=ft.Text(f"Sei sicuro di voler ripristinare il backup:\n{backup_name}?\n\nI dati attuali verranno sovrascritti!"),
                    actions=[
                        ft.TextButton("Annulla", on_click=cancel_restore),
                        ft.ElevatedButton(
                            "Ripristina",
                            on_click=confirm_restore,
                            bgcolor=ft.Colors.BLUE_600,
                            color=ft.Colors.WHITE
                        )
                    ],
                    actions_alignment=ft.MainAxisAlignment.SPACE_BETWEEN
                )

                # Use proper dialog method
                self.app.page.open(confirm_dialog)

            # Create backup list content
            if not backups:
                content = ft.Column([
                    ft.Icon(ft.Icons.CLOUD_OFF, size=48, color=ft.Colors.GREY_400),
                    ft.Text("Nessun backup trovato", size=16, weight=ft.FontWeight.BOLD),
                    ft.Text("Crea il primo backup per iniziare", size=12, color=ft.Colors.GREY_600)
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=12)
            else:
                backup_items = []
                for backup in backups:
                    # Format date
                    from datetime import datetime
                    created_date = datetime.fromisoformat(backup['created'].replace('Z', '+00:00'))
                    date_str = created_date.strftime("%d/%m/%Y %H:%M")

                    # Create backup item
                    backup_item = ft.Container(
                        content=ft.Row([
                            ft.Column([
                                ft.Text(backup['name'], weight=ft.FontWeight.BOLD, size=14),
                                ft.Text(f"📅 {date_str}", size=12, color=ft.Colors.GREY_600),
                                ft.Text(f"📦 {backup['size_mb']} MB", size=12, color=ft.Colors.GREY_600)
                            ], spacing=2, expand=True),

                            ft.Row([
                                ft.IconButton(
                                    icon=ft.Icons.RESTORE,
                                    tooltip="Ripristina",
                                    bgcolor=ft.Colors.BLUE_600,
                                    icon_color=ft.Colors.WHITE,
                                    on_click=lambda e, bid=backup['id'], bname=backup['name']: restore_backup(bid, bname)
                                ),
                                ft.IconButton(
                                    icon=ft.Icons.DELETE,
                                    tooltip="Elimina",
                                    bgcolor=ft.Colors.RED_600,
                                    icon_color=ft.Colors.WHITE,
                                    on_click=lambda e, bid=backup['id'], bname=backup['name']: delete_backup(bid, bname)
                                )
                            ], spacing=4)
                        ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                        padding=ft.padding.all(12),
                        border=ft.border.all(1, ft.Colors.GREY_300),
                        border_radius=8,
                        margin=ft.margin.only(bottom=8)
                    )
                    backup_items.append(backup_item)

                content = ft.Column(
                    backup_items,
                    spacing=0,
                    scroll=ft.ScrollMode.AUTO
                )

            # Create dialog
            dialog = ft.AlertDialog(
                modal=True,
                title=ft.Row([
                    ft.Icon(ft.Icons.CLOUD, color=ft.Colors.BLUE_600),
                    ft.Text("Backup Google Drive", weight=ft.FontWeight.BOLD)
                ], spacing=8),
                content=ft.Container(
                    content=content,
                    width=500,
                    height=400
                ),
                actions=[
                    ft.TextButton("Chiudi", on_click=close_dialog)
                ],
                actions_alignment=ft.MainAxisAlignment.CENTER
            )

            # Show dialog using proper Flet method
            self.app.page.open(dialog)

        except Exception as e:
            logger.error(f"Error showing backups dialog: {e}")
            self._show_notification(f"❌ Errore caricamento backup: {str(e)}", ft.Colors.RED_600)
