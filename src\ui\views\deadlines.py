#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vista Scadenze per Agevolami PM
"""

import flet as ft
from typing import List, Dict, Any, Optional
from datetime import datetime, date, timedelta
from uuid import UUID
import os
import tempfile
from pathlib import Path
import threading

from core import get_logger
from core.models import Deadline, DeadlineStatus, Priority, Project, Client, RecurrenceType
from core.services import EmailService

# Google Calendar integration
try:
    from core.services.google_calendar_service import GoogleCalendarService
    GOOGLE_CALENDAR_AVAILABLE = True
except ImportError:
    GOOGLE_CALENDAR_AVAILABLE = False

# PDF generation imports
try:
    from reportlab.lib.pagesizes import A4
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib import colors
    from reportlab.lib.enums import TA_CENTER, TA_LEFT
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False

logger = get_logger(__name__)

class DeadlinesView:
    """Vista per la gestione delle scadenze"""
    
    def __init__(self, app_instance):
        self.app = app_instance
        self.db_manager = app_instance.db_manager

        # Use background service manager for Google services
        from core.services.background_service_manager import get_google_calendar_service
        self._get_google_service = get_google_calendar_service
        
        # Stato della vista
        self.current_deadlines: List[Deadline] = []
        self.current_projects: Dict[UUID, Project] = {}
        self.current_clients: Dict[UUID, Client] = {}
        self.selected_deadline: Optional[Deadline] = None
        self.filter_status = None
        self.filter_priority = None
        self.filter_period = "all"
        self.search_query = ""
        self.hide_completed = True  # Hide completed deadlines by default
        self.filter_year = "current"  # current, all
        
        # Form state
        self.show_form = False
        
        # Filtered data
        self.filtered_deadlines = []
        
        # Google Calendar integration
        # self.google_service = GoogleCalendarService()  # Removed automatic initialization
        
        # Search debouncing
        self.search_timer = None
        
        # Componenti UI
        self.search_field = None
        
        self._init_components()
    
    def _init_components(self):
        """Inizializza i componenti della vista"""
        # Campo di ricerca
        self.search_field = ft.TextField(
            label="Cerca scadenze...",
            prefix_icon=ft.Icons.SEARCH,
            on_change=self._on_search_change,
            expand=True
        )
    
    def _on_search_change(self, e):
        """Gestisce il cambio del testo di ricerca con debouncing"""
        # Cancella il timer precedente se esiste
        if self.search_timer:
            self.search_timer.cancel()
        
        # Imposta la nuova query di ricerca
        self.search_query = e.control.value.lower()
        
        # Se la query è vuota, filtra immediatamente
        if not self.search_query.strip():
            self._filter_deadlines()
            return
        
        # Altrimenti, crea un nuovo timer per il debouncing (500ms)
        self.search_timer = threading.Timer(0.5, self._filter_deadlines)
        self.search_timer.start()
    
    def _filter_deadlines(self):
        """Filtra le scadenze in base ai criteri"""
        filtered = self.current_deadlines.copy()

        # Filter by completion status (hide completed if enabled)
        if self.hide_completed:
            filtered = [d for d in filtered if d.status != DeadlineStatus.COMPLETED]

        # Filter by year (show only current year by default)
        today = date.today()
        if self.filter_year == "current":
            filtered = [d for d in filtered if d.due_date.year == today.year]

        # Filtro per testo
        if self.search_query:
            filtered = [
                d for d in filtered
                if (self.search_query in d.title.lower() or
                    self.search_query in d.description.lower())
            ]

        # Filtro per status (solo se impostato)
        if self.filter_status is not None:
            filtered = [d for d in filtered if d.status == self.filter_status]

        # Filtro per priorità (solo se impostato)
        if self.filter_priority is not None:
            filtered = [d for d in filtered if d.priority == self.filter_priority]
        
        # Filtro per periodo
        if self.filter_period == "today":
            filtered = [d for d in filtered if d.due_date == today]
        elif self.filter_period == "week":
            week_end = today + timedelta(days=7)
            filtered = [d for d in filtered if today <= d.due_date <= week_end]
        elif self.filter_period == "month":
            month_end = today + timedelta(days=30)
            filtered = [d for d in filtered if today <= d.due_date <= month_end]
        elif self.filter_period == "overdue":
            filtered = [d for d in filtered if d.due_date < today and d.status != DeadlineStatus.COMPLETED]
        
        # Ordina per data di scadenza
        filtered.sort(key=lambda x: (x.due_date, x.priority))
        
        self.filtered_deadlines = filtered
        self._update_deadlines_list()
    
    def _create_header(self) -> ft.Container:
        """Crea l'header della vista"""
        # Calcola statistiche rapide
        today = date.today()
        overdue_count = len([d for d in self.current_deadlines if d.due_date < today and d.status != DeadlineStatus.COMPLETED])
        today_count = len([d for d in self.current_deadlines if d.due_date == today and d.status != DeadlineStatus.COMPLETED])
        week_count = len([d for d in self.current_deadlines if today <= d.due_date <= today + timedelta(days=7) and d.status != DeadlineStatus.COMPLETED])

        return ft.Container(
            content=ft.Row([
                # Title and stats in compact layout
                ft.Column([
                    ft.Text(
                        "Gestione Scadenze",
                        size=20,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.GREY_800
                    ),
                    ft.Text(
                        f"{len(self.current_deadlines)} scadenze totali",
                        size=11,
                        color=ft.Colors.GREY_500
                    )
                ], spacing=2),

                # Quick stats - horizontal and compact
                ft.Row([
                    self._create_quick_stat("Scadute", overdue_count, ft.Colors.RED_600, ft.Icons.ERROR),
                    self._create_quick_stat("Oggi", today_count, ft.Colors.ORANGE_600, ft.Icons.TODAY),
                    self._create_quick_stat("Settimana", week_count, ft.Colors.BLUE_600, ft.Icons.DATE_RANGE),
                ], spacing=8),

                ft.Container(expand=True),

                # Action buttons - more compact
                ft.Row([
                    ft.IconButton(
                        icon=ft.Icons.SYNC,
                        tooltip="Sincronizza con Google Calendar",
                        on_click=self._sync_google_calendar,
                        bgcolor=ft.Colors.TEAL_50,
                        icon_color=ft.Colors.TEAL_600,
                        icon_size=18
                    ),
                    ft.ElevatedButton(
                        text="Esporta",
                        icon=ft.Icons.DOWNLOAD,
                        on_click=lambda _: self._show_export_dialog(),
                        bgcolor=ft.Colors.GREEN_600,
                        color=ft.Colors.WHITE,
                        height=36
                    ),
                    ft.ElevatedButton(
                        text="Nuova Scadenza",
                        icon=ft.Icons.ADD,
                        on_click=lambda _: self._show_deadline_form(),
                        bgcolor=ft.Colors.BLUE_600,
                        color=ft.Colors.WHITE,
                        height=36
                    )
                ], spacing=8)
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            padding=ft.padding.only(bottom=12)
        )
    
    def _create_quick_stat(self, label: str, count: int, color: str, icon: str) -> ft.Container:
        """Crea una statistica rapida"""
        return ft.Container(
            content=ft.Row([
                ft.Icon(
                    icon,
                    size=14,
                    color=color
                ),
                ft.Text(
                    f"{count}",
                    size=12,
                    weight=ft.FontWeight.BOLD,
                    color=color
                ),
                ft.Text(
                    label,
                    size=10,
                    color=ft.Colors.GREY_600
                )
            ], spacing=3),
            padding=ft.padding.symmetric(horizontal=8, vertical=4),
            bgcolor=ft.Colors.GREY_50,
            border_radius=6,
            border=ft.border.all(1, color)
        )
    
    def _create_filters_bar(self) -> ft.Container:
        """Crea la barra dei filtri"""
        # Filtri periodo
        period_chips = []
        periods = [
            ("Tutte", "all", ft.Colors.GREY_600),
            ("Scadute", "overdue", ft.Colors.RED_600),
            ("Oggi", "today", ft.Colors.ORANGE_600),
            ("Settimana", "week", ft.Colors.BLUE_600),
            ("Mese", "month", ft.Colors.GREEN_600)
        ]

        for label, period, color in periods:
            is_selected = self.filter_period == period

            chip = ft.Container(
                content=ft.Text(
                    label,
                    size=10,
                    color=ft.Colors.WHITE if is_selected else color,
                    weight=ft.FontWeight.W_500
                ),
                padding=ft.padding.symmetric(horizontal=8, vertical=4),
                bgcolor=color if is_selected else ft.Colors.TRANSPARENT,
                border_radius=12,
                border=ft.border.all(1, color),
                on_click=lambda _, p=period: self._set_period_filter(p)
            )
            period_chips.append(chip)

        # Filtri priorità
        priority_chips = []
        priorities = [
            ("Tutte", None, ft.Colors.GREY_600),
            ("Critica", Priority.CRITICAL, ft.Colors.RED_600),
            ("Alta", Priority.HIGH, ft.Colors.ORANGE_600),
            ("Media", Priority.MEDIUM, ft.Colors.BLUE_600),
            ("Bassa", Priority.LOW, ft.Colors.GREEN_600)
        ]

        for label, priority, color in priorities:
            is_selected = self.filter_priority == priority

            chip = ft.Container(
                content=ft.Text(
                    label,
                    size=10,
                    color=ft.Colors.WHITE if is_selected else color,
                    weight=ft.FontWeight.W_500
                ),
                padding=ft.padding.symmetric(horizontal=8, vertical=4),
                bgcolor=color if is_selected else ft.Colors.TRANSPARENT,
                border_radius=12,
                border=ft.border.all(1, color),
                on_click=lambda _, p=priority: self._set_priority_filter(p)
            )
            priority_chips.append(chip)

        return ft.Container(
            content=ft.Column([
                # Search and refresh in one compact row
                ft.Row([
                    self.search_field,
                    ft.IconButton(
                        icon=ft.Icons.REFRESH,
                        tooltip="Aggiorna",
                        on_click=lambda _: self.refresh_data(),
                        icon_size=18
                    )
                ], spacing=8),

                # Filters in a more compact layout
                ft.Row([
                    # Period filters
                    ft.Text("Periodo:", size=11, color=ft.Colors.GREY_600, weight=ft.FontWeight.W_500),
                    *period_chips,

                    # Separator
                    ft.Container(width=1, height=20, bgcolor=ft.Colors.GREY_300),

                    # Priority filters
                    ft.Text("Priorità:", size=11, color=ft.Colors.GREY_600, weight=ft.FontWeight.W_500),
                    *priority_chips
                ], spacing=6, wrap=True),

                # Toggle options in compact row
                ft.Row([
                    # Hide completed toggle
                    ft.Container(
                        content=ft.Row([
                            ft.Icon(
                                ft.Icons.VISIBILITY_OFF if self.hide_completed else ft.Icons.VISIBILITY,
                                size=14,
                                color=ft.Colors.BLUE_600 if self.hide_completed else ft.Colors.GREY_600
                            ),
                            ft.Text(
                                "Nascondi completate" if self.hide_completed else "Mostra completate",
                                size=10,
                                color=ft.Colors.BLUE_600 if self.hide_completed else ft.Colors.GREY_600
                            )
                        ], spacing=4),
                        padding=ft.padding.symmetric(horizontal=8, vertical=4),
                        border_radius=6,
                        border=ft.border.all(1, ft.Colors.BLUE_600 if self.hide_completed else ft.Colors.GREY_400),
                        bgcolor=ft.Colors.BLUE_50 if self.hide_completed else ft.Colors.TRANSPARENT,
                        on_click=self._toggle_hide_completed,
                        tooltip="Clicca per mostrare/nascondere le scadenze completate"
                    ),

                    # Year filter toggle
                    ft.Container(
                        content=ft.Row([
                            ft.Icon(
                                ft.Icons.DATE_RANGE,
                                size=14,
                                color=ft.Colors.GREEN_600 if self.filter_year == "current" else ft.Colors.GREY_600
                            ),
                            ft.Text(
                                "Solo anno corrente" if self.filter_year == "current" else "Tutti gli anni",
                                size=10,
                                color=ft.Colors.GREEN_600 if self.filter_year == "current" else ft.Colors.GREY_600
                            )
                        ], spacing=4),
                        padding=ft.padding.symmetric(horizontal=8, vertical=4),
                        border_radius=6,
                        border=ft.border.all(1, ft.Colors.GREEN_600 if self.filter_year == "current" else ft.Colors.GREY_400),
                        bgcolor=ft.Colors.GREEN_50 if self.filter_year == "current" else ft.Colors.TRANSPARENT,
                        on_click=self._toggle_year_filter,
                        tooltip="Clicca per mostrare solo l'anno corrente o tutti gli anni"
                    )
                ], spacing=8, wrap=True)
            ], spacing=8),
            padding=ft.padding.only(bottom=10)
        )
    
    def _set_period_filter(self, period: str):
        """Imposta il filtro per periodo"""
        self.filter_period = period
        self._filter_deadlines()
    
    def _set_priority_filter(self, priority: Optional[Priority]):
        """Imposta il filtro per priorità"""
        self.filter_priority = priority
        self._filter_deadlines()

    def _toggle_hide_completed(self, e):
        """Toggle hiding completed deadlines"""
        self.hide_completed = not self.hide_completed
        self._filter_deadlines()

    def _toggle_year_filter(self, e):
        """Toggle year filtering between current year and all years"""
        self.filter_year = "all" if self.filter_year == "current" else "current"
        self._filter_deadlines()

    def _create_deadlines_list(self) -> ft.Container:
        """Crea la lista delle scadenze"""
        if not self.filtered_deadlines:
            return ft.Container(
                content=ft.Column([
                    ft.Icon(
                        ft.Icons.EVENT_AVAILABLE,
                        size=48,
                        color=ft.Colors.GREY_400
                    ),
                    ft.Text(
                        "Nessuna scadenza trovata" if self.search_query or self.filter_period != "all" else "Nessuna scadenza presente",
                        size=16,
                        color=ft.Colors.GREY_500,
                        weight=ft.FontWeight.W_500
                    ),
                    ft.Text(
                        "Prova a modificare i filtri" if self.search_query or self.filter_period != "all" else "Crea la prima scadenza",
                        size=12,
                        color=ft.Colors.GREY_400
                    )
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=8),
                padding=ft.padding.all(40),
                alignment=ft.alignment.center,
                expand=True
            )
        
        # Crea gli elementi della lista
        deadline_items = []
        for deadline in self.filtered_deadlines:
            deadline_items.append(self._create_deadline_item(deadline))
        
        return ft.Container(
            content=ft.Column(
                controls=deadline_items,
                spacing=4,
                scroll=ft.ScrollMode.AUTO
            ),
            expand=True
        )
    
    def _create_deadline_item(self, deadline: Deadline) -> ft.Container:
        """Crea un elemento scadenza"""
        # Trova progetto e cliente
        project = None
        client = None
        
        if deadline.project_id:
            try:
                project = self.current_projects.get(deadline.project_id)
                if project and project.client_id:
                    client = self.current_clients.get(project.client_id)
            except:
                pass
        
        # Calcola giorni rimanenti
        days_remaining = (deadline.due_date - date.today()).days
        is_overdue = days_remaining < 0
        
        # Colori per priorità
        priority_colors = {
            Priority.CRITICAL: ft.Colors.RED_600,
            Priority.HIGH: ft.Colors.ORANGE_600,
            Priority.MEDIUM: ft.Colors.BLUE_600,
            Priority.LOW: ft.Colors.GREEN_600
        }
        
        priority_color = priority_colors.get(deadline.priority, ft.Colors.GREY_600)
        
        # Colori per status
        status_colors = {
            DeadlineStatus.PENDING: ft.Colors.ORANGE_600,
            DeadlineStatus.COMPLETED: ft.Colors.GREEN_600,
            DeadlineStatus.OVERDUE: ft.Colors.RED_600,
            DeadlineStatus.CANCELLED: ft.Colors.GREY_600
        }
        
        status_color = status_colors.get(deadline.status, ft.Colors.GREY_600)
        
        # Icona e testo per urgenza
        if is_overdue and deadline.status != DeadlineStatus.COMPLETED:
            urgency_icon = ft.Icons.ERROR
            urgency_color = ft.Colors.RED_600
            urgency_text = f"Scaduto da {abs(days_remaining)} giorni"
        elif days_remaining == 0:
            urgency_icon = ft.Icons.WARNING
            urgency_color = ft.Colors.ORANGE_600
            urgency_text = "Scade oggi"
        elif days_remaining <= 3:
            urgency_icon = ft.Icons.SCHEDULE
            urgency_color = ft.Colors.ORANGE_600
            urgency_text = f"Scade tra {days_remaining} giorni"
        elif days_remaining <= 7:
            urgency_icon = ft.Icons.SCHEDULE
            urgency_color = ft.Colors.BLUE_600
            urgency_text = f"Scade tra {days_remaining} giorni"
        else:
            urgency_icon = ft.Icons.EVENT
            urgency_color = ft.Colors.GREEN_600
            urgency_text = f"Scade tra {days_remaining} giorni"
        
        # Sottotitolo
        subtitle_parts = []
        if client:
            subtitle_parts.append(client.name)
        if project:
            subtitle_parts.append(project.name)
        
        subtitle = " • ".join(subtitle_parts) if subtitle_parts else "Scadenza generale"
        
        return ft.Container(
            content=ft.Column([
                # Header della scadenza
                ft.Row([
                    # Indicatore priorità
                    ft.Container(
                        width=4,
                        height=50,
                        bgcolor=priority_color,
                        border_radius=2
                    ),
                    
                    # Contenuto principale
                    ft.Column([
                        ft.Row([
                            ft.Text(
                                deadline.title,
                                size=14,
                                weight=ft.FontWeight.W_500,
                                color=ft.Colors.GREY_800,
                                expand=True
                            ),
                            
                            # Badge status
                            ft.Container(
                                content=ft.Text(
                                    str(deadline.status).replace('_', ' ').title(),
                                    size=10,
                                    color=ft.Colors.WHITE,
                                    weight=ft.FontWeight.BOLD
                                ),
                                padding=ft.padding.symmetric(horizontal=8, vertical=4),
                                bgcolor=status_color,
                                border_radius=10
                            )
                        ]),
                        
                        ft.Text(
                            subtitle,
                            size=11,
                            color=ft.Colors.GREY_500
                        ),
                        
                        ft.Text(
                            deadline.description,
                            size=11,
                            color=ft.Colors.GREY_600,
                            max_lines=2
                        ) if deadline.description else ft.Container(height=0)
                    ], spacing=4, expand=True),
                    
                    # Info data e urgenza
                    ft.Column([
                        ft.Text(
                            deadline.due_date.strftime("%d/%m/%Y"),
                            size=12,
                            color=ft.Colors.GREY_800,
                            weight=ft.FontWeight.W_500
                        ),
                        
                        ft.Row([
                            ft.Icon(
                                urgency_icon,
                                size=12,
                                color=urgency_color
                            ),
                            ft.Text(
                                urgency_text,
                                size=10,
                                color=urgency_color,
                                weight=ft.FontWeight.W_500
                            )
                        ], spacing=4)
                    ], spacing=4, horizontal_alignment=ft.CrossAxisAlignment.END),
                    
                    # Azioni
                    ft.Row([
                        ft.IconButton(
                            icon=ft.Icons.CHECK_CIRCLE if deadline.status != DeadlineStatus.COMPLETED else ft.Icons.UNDO,
                            icon_size=16,
                            tooltip="Completa" if deadline.status != DeadlineStatus.COMPLETED else "Riapri",
                            on_click=lambda _, d=deadline: self._toggle_completion(d)
                        ),
                        ft.IconButton(
                            icon=ft.Icons.EDIT,
                            icon_size=16,
                            tooltip="Modifica",
                            on_click=lambda _, d=deadline: self._edit_deadline(d)
                        ),
                        ft.IconButton(
                            icon=ft.Icons.DELETE,
                            icon_size=16,
                            tooltip="Elimina",
                            on_click=lambda _, d=deadline: self._delete_deadline(d)
                        )
                    ], spacing=0)
                ], spacing=12)
            ], spacing=6),
            padding=ft.padding.all(12),
            margin=ft.margin.symmetric(vertical=1),
            bgcolor=ft.Colors.WHITE,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.GREY_200),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=2,
                color=ft.Colors.BLACK12
            )
        )
    
    def _show_deadline_form(self, deadline: Optional[Deadline] = None):
        """Mostra il form per creare/modificare una scadenza"""
        self.selected_deadline = deadline
        self.show_form = True
        self.refresh()
        logger.info(f"Mostra form scadenza: {'modifica' if deadline else 'nuova'}")

    def cancel_current_form(self):
        """Cancel current form - called by keyboard shortcuts"""
        if self.show_form:
            self._close_form()
            logger.info("Deadline form cancelled via keyboard shortcut")

    def save_current_form(self):
        """Save current form - called by keyboard shortcuts"""
        if self.show_form:
            logger.info("Save shortcut triggered in deadline form")
            if hasattr(self, 'app') and hasattr(self.app, 'main_layout'):
                self.app.main_layout._show_shortcut_feedback("Use the Save button in the form or focus on a field and press Ctrl+S")
    
    def _toggle_completion(self, deadline: Deadline):
        """Cambia lo stato di completamento di una scadenza"""
        try:
            if deadline.status == DeadlineStatus.COMPLETED:
                deadline.status = DeadlineStatus.PENDING
                deadline.completed_date = None
                action = "riaperta"
                message = f"Scadenza '{deadline.title}' riaperta"
            else:
                deadline.status = DeadlineStatus.COMPLETED
                deadline.completed_date = date.today()
                action = "completata"
                message = f"Scadenza '{deadline.title}' completata!"
            
            deadline.updated_at = datetime.now()
            success = self.db_manager.update_deadline(deadline)

            # Enhanced Google Calendar sync with auto-sync and completion handling
            sync_success = True
            sync_message = ""
            if success and self._should_auto_sync():
                try:
                    if deadline.status == DeadlineStatus.COMPLETED and self._should_delete_completed():
                        # Delete from Google Calendar when completed (if setting enabled)
                        if hasattr(deadline, 'google_event_id') and deadline.google_event_id:
                            self.google_service.delete_deadline_from_google(deadline.google_event_id)
                            sync_message = " (Rimossa da Google Calendar)"
                            logger.info(f"Completed deadline removed from Google Calendar: {deadline.google_event_id}")
                        else:
                            sync_message = " (Completata)"
                    else:
                        # Update or create in Google Calendar
                        google_event_id = self.google_service.sync_deadline_to_google(deadline)
                        if google_event_id:
                            if not hasattr(deadline, 'google_event_id') or deadline.google_event_id != google_event_id:
                                deadline.google_event_id = google_event_id
                                self.db_manager.update_deadline(deadline)
                            sync_message = " (Sincronizzata con Google Calendar)"
                            logger.info(f"Deadline status synced to Google Calendar: {google_event_id}")
                        else:
                            sync_message = " (Errore sincronizzazione Google Calendar)"
                            sync_success = False

                except Exception as e:
                    logger.warning(f"Failed to sync deadline status to Google Calendar: {e}")
                    sync_message = " (Errore sincronizzazione Google Calendar)"
                    sync_success = False

            # Update message with sync status
            message += sync_message
            
            if success:
                # Aggiorna alert correlati
                self.app.alert_service.complete_deadline_alerts(deadline.id)
                self.refresh_data()
                
                # Mostra messaggio di successo
                self._show_success_dialog("Successo", message)
                
                # Notifica di sistema
                self._show_system_notification("Agevolami PM", message)
                
                logger.info(f"Scadenza {action}: {deadline.title}")
            else:
                self._show_error_dialog("Errore", f"Errore durante l'aggiornamento della scadenza")
            
        except Exception as e:
            logger.error(f"Errore toggle completamento: {e}")
            self._show_error_dialog("Errore", f"Errore imprevisto: {str(e)}")
    
    def _edit_deadline(self, deadline: Deadline):
        """Modifica una scadenza"""
        self._show_deadline_form(deadline)
    
    def _delete_deadline(self, deadline: Deadline):
        """Elimina una scadenza"""
        def confirm_delete(e):
            self.app.page.close(dialog)
            
            # Enhanced Google Calendar deletion with better feedback
            sync_success = True
            sync_message = ""
            if (hasattr(deadline, 'google_event_id') and deadline.google_event_id and
                self._should_auto_sync()):
                try:
                    self.google_service.delete_deadline_from_google(deadline.google_event_id)
                    sync_message = " (Rimossa anche da Google Calendar)"
                    logger.info(f"Deadline removed from Google Calendar: {deadline.google_event_id}")
                except Exception as e:
                    sync_message = " (Errore rimozione da Google Calendar)"
                    sync_success = False
                    logger.warning(f"Failed to remove deadline from Google Calendar: {e}")
                    # Continue with local deletion even if Google sync fails

            if self.db_manager.delete_deadline(deadline.id):
                # Completa alert correlati
                self.app.alert_service.complete_deadline_alerts(deadline.id)

                # Aggiorna dati
                self.refresh_data()

                # Messaggio di successo con feedback sync
                message = f"Scadenza '{deadline.title}' eliminata con successo!{sync_message}"

                self._show_success_dialog("Successo", message)
                
                # Notifica di sistema
                self._show_system_notification("Agevolami PM", message)
                
                logger.info(f"Scadenza eliminata: {deadline.title}")
            else:
                self._show_error_dialog("Errore", "Errore durante l'eliminazione della scadenza")
        
        def cancel_delete(e):
            self.app.page.close(dialog)
        
        # Show info about Google Calendar sync in dialog
        sync_info = ""
        if hasattr(deadline, 'google_event_id') and deadline.google_event_id:
            sync_info = "\n\nNota: Questa scadenza verrà rimossa anche da Google Calendar."
        
        dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text("Conferma eliminazione"),
            content=ft.Text(f"Sei sicuro di voler eliminare la scadenza '{deadline.title}'?\n\nQuesta azione non può essere annullata.{sync_info}"),
            actions=[
                ft.TextButton("Annulla", on_click=cancel_delete),
                ft.TextButton(
                    "Elimina", 
                    on_click=confirm_delete,
                    style=ft.ButtonStyle(color=ft.Colors.RED)
                ),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        
        self.app.page.open(dialog)
    
    def _update_deadlines_list(self):
        """Aggiorna la lista delle scadenze"""
        # Forza l'aggiornamento della UI ricostruendo completamente la view
        if hasattr(self, 'app') and hasattr(self.app, 'page') and self.app.page:
            # Rebuild the entire view content
            new_content = self.build()
            
            # Update the main layout's content area
            if hasattr(self.app, 'main_layout') and hasattr(self.app.main_layout, 'content_area'):
                self.app.main_layout.content_area.content = new_content
            
            # Trigger page update
            self.app.page.update()
    
    def _close_form(self):
        """Chiude il form"""
        self.show_form = False
        self.selected_deadline = None
        self.refresh()
    
    def build(self) -> ft.Container:
        """Costruisce la vista scadenze"""
        if self.show_form:
            return self._build_deadline_form()

        return ft.Container(
            content=ft.Column([
                # Header
                self._create_header(),

                # Filtri
                self._create_filters_bar(),

                # Lista scadenze
                self._create_deadlines_list()
            ], spacing=0),
            padding=ft.padding.symmetric(horizontal=16, vertical=12),
            expand=True
        )
    
    def _build_deadline_form(self) -> ft.Container:
        """Costruisce il form per scadenza con UI migliorata"""
        from ui.components.date_picker import DatePicker

        is_edit = self.selected_deadline is not None
        title = "Modifica Scadenza" if is_edit else "Nuova Scadenza"

        # Dropdown progetti
        project_options = [ft.dropdown.Option(text="Nessun progetto", key="")]
        project_options.extend([ft.dropdown.Option(text=f"{p.name}", key=str(p.id)) for p in self.current_projects.values()])

        project_dropdown = ft.Dropdown(
            label="Progetto",
            options=project_options,
            value=str(self.selected_deadline.project_id) if is_edit and self.selected_deadline.project_id else "",
            expand=True,
            bgcolor=ft.Colors.WHITE,
            border_color=ft.Colors.GREY_300,
            border_radius=8,
            content_padding=ft.padding.symmetric(horizontal=12, vertical=8)
        )

        # Dropdown priorità con stile migliorato e icone colorate
        priority_options = []
        priority_colors = {
            Priority.CRITICAL.value: ("🔴", ft.Colors.RED_600),
            Priority.HIGH.value: ("🟠", ft.Colors.ORANGE_600),
            Priority.MEDIUM.value: ("🔵", ft.Colors.BLUE_600),
            Priority.LOW.value: ("🟢", ft.Colors.GREEN_600)
        }

        for p in Priority:
            icon, color = priority_colors.get(p.value, ("⚪", ft.Colors.GREY_600))
            priority_options.append(
                ft.dropdown.Option(
                    text=f"{icon} {p.value.title()}",
                    key=p.value
                )
            )

        current_priority = self.selected_deadline.priority.value if is_edit and hasattr(self.selected_deadline.priority, 'value') else (str(self.selected_deadline.priority) if is_edit else Priority.MEDIUM.value)

        priority_dropdown = ft.Dropdown(
            label="Priorità *",
            options=priority_options,
            value=current_priority,
            expand=True,
            bgcolor=ft.Colors.WHITE,
            border_color=ft.Colors.GREY_300,
            border_radius=8,
            content_padding=ft.padding.symmetric(horizontal=12, vertical=8)
        )

        # Campi del form con stile migliorato
        title_field = ft.TextField(
            label="Titolo Scadenza *",
            value=self.selected_deadline.title if is_edit else "",
            expand=True,
            bgcolor=ft.Colors.WHITE,
            border_color=ft.Colors.GREY_300,
            border_radius=8,
            content_padding=ft.padding.symmetric(horizontal=12, vertical=8),
            text_style=ft.TextStyle(size=14)
        )

        description_field = ft.TextField(
            label="Descrizione",
            value=self.selected_deadline.description if is_edit else "",
            multiline=True,
            min_lines=3,
            max_lines=5,
            bgcolor=ft.Colors.WHITE,
            border_color=ft.Colors.GREY_300,
            border_radius=8,
            content_padding=ft.padding.symmetric(horizontal=12, vertical=8),
            text_style=ft.TextStyle(size=14)
        )

        # Date picker per la data di scadenza
        due_date_picker = DatePicker(
            label="Data Scadenza *",
            value=self.selected_deadline.due_date if is_edit else None,
            min_date=date.today() - timedelta(days=365),  # Permette date passate per modifiche
            max_date=date.today() + timedelta(days=365*5)  # 5 anni nel futuro
        )

        # Dropdown ricorrenza con icone
        recurrence_options = []
        recurrence_icons = {
            RecurrenceType.NONE.value: "🚫",
            RecurrenceType.DAILY.value: "📅",
            RecurrenceType.WEEKLY.value: "📆",
            RecurrenceType.MONTHLY.value: "🗓️",
            RecurrenceType.QUARTERLY.value: "📊",
            RecurrenceType.YEARLY.value: "🎯",
            RecurrenceType.CUSTOM.value: "⚙️"
        }

        for r in RecurrenceType:
            icon = recurrence_icons.get(r.value, "📋")
            recurrence_options.append(
                ft.dropdown.Option(
                    text=f"{icon} {r.value.title()}",
                    key=r.value
                )
            )

        recurrence_dropdown = ft.Dropdown(
            label="Ricorrenza",
            options=recurrence_options,
            value=self.selected_deadline.recurrence_type.value if is_edit and hasattr(self.selected_deadline.recurrence_type, 'value') else RecurrenceType.NONE.value,
            expand=True,
            bgcolor=ft.Colors.WHITE,
            border_color=ft.Colors.GREY_300,
            border_radius=8,
            content_padding=ft.padding.symmetric(horizontal=12, vertical=8),
            on_change=self._on_recurrence_change
        )

        # Campo intervallo ricorrenza (inizialmente nascosto)
        self.recurrence_interval_field = ft.TextField(
            label="Ogni (numero)",
            value=str(self.selected_deadline.recurrence_interval) if is_edit and self.selected_deadline.recurrence_type != RecurrenceType.NONE else "1",
            keyboard_type=ft.KeyboardType.NUMBER,
            width=120,
            bgcolor=ft.Colors.WHITE,
            border_color=ft.Colors.GREY_300,
            border_radius=8,
            content_padding=ft.padding.symmetric(horizontal=12, vertical=8),
            visible=is_edit and self.selected_deadline.recurrence_type != RecurrenceType.NONE
        )
        
        # Testo descrittivo ricorrenza
        self.recurrence_description = ft.Text(
            self._get_recurrence_description(recurrence_dropdown.value, self.recurrence_interval_field.value),
            size=12,
            color=ft.Colors.GREY_600,
            visible=is_edit and self.selected_deadline.recurrence_type != RecurrenceType.NONE
        )
        
        # Date picker per la fine ricorrenza
        self.recurrence_end_picker = DatePicker(
            label="Fine Ricorrenza (opzionale)",
            value=self.selected_deadline.recurrence_end_date if is_edit and self.selected_deadline.recurrence_end_date else None,
            min_date=date.today(),
            max_date=date.today() + timedelta(days=365*10),
            visible=is_edit and self.selected_deadline.recurrence_type != RecurrenceType.NONE
        )

        # Checkbox per notifiche email con stile migliorato
        email_notifications_checkbox = ft.Checkbox(
            label="📧 Notifiche Email",
            value=self.selected_deadline.email_notifications if is_edit else True,
            check_color=ft.Colors.BLUE_600,
            active_color=ft.Colors.BLUE_100
        )

        # Campo giorni di anticipo per alert
        alert_days_field = ft.TextField(
            label="⏰ Giorni Anticipo Alert",
            value=str(self.selected_deadline.alert_days_before) if is_edit else "15",
            keyboard_type=ft.KeyboardType.NUMBER,
            width=180,
            bgcolor=ft.Colors.WHITE,
            border_color=ft.Colors.GREY_300,
            border_radius=8,
            content_padding=ft.padding.symmetric(horizontal=12, vertical=8),
            text_style=ft.TextStyle(size=14)
        )
        
        return ft.Container(
            content=ft.Column([
                # Modern Header with gradient background
                ft.Container(
                    content=ft.Row([
                        ft.IconButton(
                            icon=ft.Icons.ARROW_BACK_IOS,
                            on_click=lambda _: self._close_form(),
                            icon_color=ft.Colors.WHITE,
                            bgcolor=ft.Colors.with_opacity(0.2, ft.Colors.WHITE),
                            style=ft.ButtonStyle(
                                shape=ft.CircleBorder()
                            )
                        ),
                        ft.Container(width=12),
                        ft.Column([
                            ft.Text(
                                title,
                                size=26,
                                weight=ft.FontWeight.BOLD,
                                color=ft.Colors.WHITE
                            ),
                            ft.Text(
                                "Gestisci le tue scadenze con priorità e ricorrenze",
                                size=14,
                                color=ft.Colors.with_opacity(0.9, ft.Colors.WHITE)
                            )
                        ], spacing=4),
                        ft.Container(expand=True)
                    ]),
                    padding=ft.padding.all(24),
                    gradient=ft.LinearGradient(
                        colors=[ft.Colors.BLUE_600, ft.Colors.BLUE_800],
                        begin=ft.alignment.top_left,
                        end=ft.alignment.bottom_right
                    ),
                    border_radius=ft.border_radius.only(top_left=16, top_right=16),
                    margin=ft.margin.only(left=-24, right=-24, top=-24)
                ),

                # Scrollable form content with modern card-based layout
                ft.Container(
                    content=ft.ListView(
                        controls=[
                            # Priority Section - Featured prominently at the top
                            ft.Container(
                                content=ft.Column([
                                    ft.Row([
                                        ft.Icon(ft.Icons.PRIORITY_HIGH, color=ft.Colors.RED_600, size=24),
                                        ft.Text(
                                            "Priorità Scadenza",
                                            size=20,
                                            weight=ft.FontWeight.BOLD,
                                            color=ft.Colors.GREY_800
                                        )
                                    ], spacing=12),
                                    ft.Container(height=16),
                                    ft.Container(
                                        content=priority_dropdown,
                                        bgcolor=ft.Colors.GREY_50,
                                        border_radius=12,
                                        padding=ft.padding.all(16),
                                        border=ft.border.all(2, ft.Colors.BLUE_200)
                                    ),
                                    ft.Container(height=8),
                                    ft.Text(
                                        "💡 Seleziona la priorità per organizzare meglio le tue scadenze",
                                        size=12,
                                        color=ft.Colors.GREY_600,
                                        italic=True
                                    )
                                ]),
                                bgcolor=ft.Colors.WHITE,
                                border=ft.border.all(2, ft.Colors.BLUE_300),
                                border_radius=16,
                                padding=24,
                                margin=ft.margin.only(bottom=20),
                                shadow=ft.BoxShadow(
                                    spread_radius=0,
                                    blur_radius=8,
                                    color=ft.Colors.with_opacity(0.1, ft.Colors.BLUE_600),
                                    offset=ft.Offset(0, 2)
                                )
                            ),

                            # Basic Information Section
                            ft.Container(
                                content=ft.Column([
                                    ft.Row([
                                        ft.Icon(ft.Icons.INFO_OUTLINE, color=ft.Colors.BLUE_600, size=20),
                                        ft.Text(
                                            "Informazioni Base",
                                            size=18,
                                            weight=ft.FontWeight.W_600,
                                            color=ft.Colors.GREY_800
                                        )
                                    ], spacing=8),
                                    ft.Container(height=16),
                                    title_field,
                                    ft.Container(height=12),
                                    project_dropdown,
                                    ft.Container(height=12),
                                    description_field,
                                    ft.Container(height=12),
                                    due_date_picker
                                ]),
                                bgcolor=ft.Colors.WHITE,
                                border=ft.border.all(1, ft.Colors.GREY_200),
                                border_radius=16,
                                padding=24,
                                margin=ft.margin.only(bottom=16),
                                shadow=ft.BoxShadow(
                                    spread_radius=0,
                                    blur_radius=4,
                                    color=ft.Colors.with_opacity(0.05, ft.Colors.BLACK),
                                    offset=ft.Offset(0, 2)
                                )
                            ),

                            # Recurrence Section
                            ft.Container(
                                content=ft.Column([
                                    ft.Row([
                                        ft.Icon(ft.Icons.REPEAT, color=ft.Colors.PURPLE_600, size=20),
                                        ft.Text(
                                            "Impostazioni Ricorrenza",
                                            size=18,
                                            weight=ft.FontWeight.W_600,
                                            color=ft.Colors.GREY_800
                                        )
                                    ], spacing=8),
                                    ft.Container(height=16),
                                    recurrence_dropdown,
                                    ft.Container(height=12),
                                    ft.Row([
                                        self.recurrence_interval_field,
                                        ft.Container(expand=True)
                                    ]),
                                    ft.Container(height=8),
                                    self.recurrence_description,
                                    ft.Container(height=8),
                                    self.recurrence_end_picker
                                ]),
                                bgcolor=ft.Colors.WHITE,
                                border=ft.border.all(1, ft.Colors.GREY_200),
                                border_radius=16,
                                padding=24,
                                margin=ft.margin.only(bottom=16),
                                shadow=ft.BoxShadow(
                                    spread_radius=0,
                                    blur_radius=4,
                                    color=ft.Colors.with_opacity(0.05, ft.Colors.BLACK),
                                    offset=ft.Offset(0, 2)
                                )
                            ),

                            # Notifications Section
                            ft.Container(
                                content=ft.Column([
                                    ft.Row([
                                        ft.Icon(ft.Icons.NOTIFICATIONS_ACTIVE, color=ft.Colors.ORANGE_600, size=20),
                                        ft.Text(
                                            "Impostazioni Notifiche",
                                            size=18,
                                            weight=ft.FontWeight.W_600,
                                            color=ft.Colors.GREY_800
                                        )
                                    ], spacing=8),
                                    ft.Container(height=16),
                                    ft.Row([
                                        alert_days_field,
                                        ft.Container(width=20),
                                        email_notifications_checkbox
                                    ], alignment=ft.MainAxisAlignment.START),
                                    ft.Container(height=8),
                                    ft.Text(
                                        "💡 Riceverai notifiche in anticipo per non perdere le scadenze",
                                        size=12,
                                        color=ft.Colors.GREY_600,
                                        italic=True
                                    )
                                ]),
                                bgcolor=ft.Colors.WHITE,
                                border=ft.border.all(1, ft.Colors.GREY_200),
                                border_radius=16,
                                padding=24,
                                margin=ft.margin.only(bottom=24),
                                shadow=ft.BoxShadow(
                                    spread_radius=0,
                                    blur_radius=4,
                                    color=ft.Colors.with_opacity(0.05, ft.Colors.BLACK),
                                    offset=ft.Offset(0, 2)
                                )
                            ),

                            # Action Buttons with modern styling
                            ft.Container(
                                content=ft.Row([
                                    ft.OutlinedButton(
                                        content=ft.Row([
                                            ft.Icon(ft.Icons.CLOSE, size=18),
                                            ft.Text("Annulla", size=14, weight=ft.FontWeight.W_500)
                                        ], spacing=8, alignment=ft.MainAxisAlignment.CENTER),
                                        on_click=lambda _: self._close_form(),
                                        style=ft.ButtonStyle(
                                            color=ft.Colors.GREY_700,
                                            side=ft.BorderSide(2, ft.Colors.GREY_300),
                                            padding=ft.padding.symmetric(horizontal=32, vertical=16),
                                            shape=ft.RoundedRectangleBorder(radius=12)
                                        ),
                                        height=56,
                                        width=140
                                    ),
                                    ft.Container(expand=True),
                                    ft.ElevatedButton(
                                        content=ft.Row([
                                            ft.Icon(
                                                ft.Icons.SAVE if not is_edit else ft.Icons.UPDATE,
                                                size=18,
                                                color=ft.Colors.WHITE
                                            ),
                                            ft.Text(
                                                "Salva" if not is_edit else "Aggiorna",
                                                size=14,
                                                weight=ft.FontWeight.W_600,
                                                color=ft.Colors.WHITE
                                            )
                                        ], spacing=8, alignment=ft.MainAxisAlignment.CENTER),
                                        on_click=lambda _: self._save_deadline({
                                            'title': title_field.value,
                                            'description': description_field.value,
                                            'due_date': due_date_picker.value.strftime("%d/%m/%Y") if due_date_picker.value else "",
                                            'priority': priority_dropdown.value,
                                            'project_id': project_dropdown.value,
                                            'recurrence_type': recurrence_dropdown.value,
                                            'recurrence_interval': self.recurrence_interval_field.value,
                                            'recurrence_end_date': self.recurrence_end_picker.value.strftime("%d/%m/%Y") if self.recurrence_end_picker.value else "",
                                            'email_notifications': email_notifications_checkbox.value,
                                            'alert_days_before': alert_days_field.value
                                        }),
                                        style=ft.ButtonStyle(
                                            bgcolor=ft.Colors.BLUE_600,
                                            color=ft.Colors.WHITE,
                                            padding=ft.padding.symmetric(horizontal=32, vertical=16),
                                            elevation=4,
                                            shadow_color=ft.Colors.BLUE_200,
                                            shape=ft.RoundedRectangleBorder(radius=12)
                                        ),
                                        height=56,
                                        width=160
                                    )
                                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                                padding=ft.padding.all(24),
                                bgcolor=ft.Colors.WHITE,
                                border=ft.border.only(top=ft.BorderSide(1, ft.Colors.GREY_200)),
                                margin=ft.margin.only(left=-24, right=-24, bottom=-24)
                            )
                        ],
                        spacing=0,
                        padding=ft.padding.all(24),
                        auto_scroll=False
                    ),
                    expand=True
                )
            ]),
            bgcolor=ft.Colors.GREY_50,
            border_radius=16,
            border=ft.border.all(1, ft.Colors.GREY_200),
            padding=24,
            shadow=ft.BoxShadow(
                spread_radius=2,
                blur_radius=20,
                color=ft.Colors.with_opacity(0.15, ft.Colors.BLACK),
                offset=ft.Offset(0, 8)
            )
        )
    
    def _create_priority_visual_indicator(self, current_priority: str) -> ft.Container:
        """Crea un indicatore visivo della priorità selezionata"""
        priority_info = {
            Priority.CRITICAL.value: ("🔴", "Critica", ft.Colors.RED_600, ft.Colors.RED_50),
            Priority.HIGH.value: ("🟠", "Alta", ft.Colors.ORANGE_600, ft.Colors.ORANGE_50),
            Priority.MEDIUM.value: ("🔵", "Media", ft.Colors.BLUE_600, ft.Colors.BLUE_50),
            Priority.LOW.value: ("🟢", "Bassa", ft.Colors.GREEN_600, ft.Colors.GREEN_50)
        }

        icon, text, color, bg_color = priority_info.get(current_priority, ("⚪", "Non definita", ft.Colors.GREY_600, ft.Colors.GREY_50))

        return ft.Container(
            content=ft.Row([
                ft.Text(icon, size=20),
                ft.Column([
                    ft.Text(
                        f"Priorità: {text}",
                        size=14,
                        weight=ft.FontWeight.W_600,
                        color=color
                    ),
                    ft.Text(
                        "Livello di urgenza della scadenza",
                        size=11,
                        color=ft.Colors.GREY_600
                    )
                ], spacing=2)
            ], spacing=12),
            bgcolor=bg_color,
            border=ft.border.all(1, color),
            border_radius=12,
            padding=ft.padding.all(12)
        )

    def _on_recurrence_change(self, e):
        """Gestisce il cambio di tipo ricorrenza"""
        recurrence_type = e.control.value
        is_recurring = recurrence_type != RecurrenceType.NONE.value

        # Mostra/nasconde i campi ricorrenza
        self.recurrence_interval_field.visible = is_recurring
        self.recurrence_description.visible = is_recurring
        self.recurrence_end_picker.visible = is_recurring

        # Aggiorna la descrizione
        if is_recurring:
            self.recurrence_description.value = self._get_recurrence_description(
                recurrence_type,
                self.recurrence_interval_field.value
            )

        # Aggiorna l'interfaccia
        self.recurrence_interval_field.update()
        self.recurrence_description.update()
        self.recurrence_end_picker.update()
    
    def _get_recurrence_description(self, recurrence_type: str, interval: str) -> str:
        """Genera la descrizione della ricorrenza"""
        try:
            interval_num = int(interval) if interval else 1
        except ValueError:
            interval_num = 1
        
        if recurrence_type == RecurrenceType.NONE.value:
            return ""
        elif recurrence_type == RecurrenceType.DAILY.value:
            return f"Si ripete ogni {interval_num} giorno{'i' if interval_num > 1 else 'o'}"
        elif recurrence_type == RecurrenceType.WEEKLY.value:
            return f"Si ripete ogni {interval_num} settimana{'e' if interval_num > 1 else ''}"
        elif recurrence_type == RecurrenceType.MONTHLY.value:
            return f"Si ripete ogni {interval_num} mese{'i' if interval_num > 1 else ''}"
        elif recurrence_type == RecurrenceType.QUARTERLY.value:
            return f"Si ripete ogni {interval_num * 3} mesi (trimestrale)"
        elif recurrence_type == RecurrenceType.YEARLY.value:
            return f"Si ripete ogni {interval_num} anno{'i' if interval_num > 1 else 'o'}"
        else:
            return "Ricorrenza personalizzata"
    
    def _save_deadline(self, data: Dict[str, str]):
        """Salva la scadenza"""
        try:
            # Validazione base
            if not data['title'] or not data['due_date'] or not data['priority']:
                self._show_error_dialog("Errore", "Titolo, data scadenza e priorità sono obbligatori")
                return
            
            # Parsing data
            try:
                due_date = datetime.strptime(data['due_date'], "%d/%m/%Y").date()
            except ValueError:
                self._show_error_dialog("Errore", "Formato data non valido. Usa DD/MM/YYYY")
                return
            
            # Parse recurrence end date
            recurrence_end_date = None
            if data.get('recurrence_end_date'):
                try:
                    recurrence_end_date = datetime.strptime(data['recurrence_end_date'], "%d/%m/%Y").date()
                except ValueError:
                    self._show_error_dialog("Errore", "Formato data fine ricorrenza non valido. Usa DD/MM/YYYY")
                    return
            
            # Project ID - handle empty string from dropdown
            project_id_str = data['project_id']
            if not project_id_str or project_id_str == "" or project_id_str == "Nessun progetto":
                project_id = None
            else:
                try:
                    project_id = UUID(project_id_str)
                except ValueError as e:
                    self._show_error_dialog("Errore", f"ID progetto non valido: {project_id_str}")
                    return
            
            # Parse other fields
            recurrence_type = RecurrenceType(data.get('recurrence_type', RecurrenceType.NONE.value))
            recurrence_interval = int(data.get('recurrence_interval', '1')) if data.get('recurrence_interval') else 1
            email_notifications = data.get('email_notifications', True)
            alert_days_before = int(data.get('alert_days_before', '15')) if data.get('alert_days_before') else 15
            sync_to_google = data.get('sync_to_google', True)  # New field for Google Calendar sync
            
            if self.selected_deadline:
                # Modifica scadenza esistente
                self.selected_deadline.title = data['title']
                self.selected_deadline.project_id = project_id
                self.selected_deadline.priority = Priority(data['priority'])
                self.selected_deadline.due_date = due_date
                self.selected_deadline.description = data['description']
                self.selected_deadline.recurrence_type = recurrence_type
                self.selected_deadline.recurrence_interval = recurrence_interval
                self.selected_deadline.recurrence_end_date = recurrence_end_date
                self.selected_deadline.is_recurring = recurrence_type != RecurrenceType.NONE
                self.selected_deadline.email_notifications = email_notifications
                self.selected_deadline.alert_days_before = alert_days_before
                self.selected_deadline.sync_to_google = sync_to_google
                self.selected_deadline.updated_at = datetime.now()
                
                success = self.db_manager.update_deadline(self.selected_deadline)

                # Enhanced Google Calendar sync with improved feedback
                sync_success = True
                sync_message = ""
                if success and sync_to_google and self._should_auto_sync():
                    try:
                        google_event_id = self.google_service.sync_deadline_to_google(self.selected_deadline)
                        if google_event_id:
                            # The service already updates the deadline object with Google event ID
                            # Now save it to the database
                            if not hasattr(self.selected_deadline, 'google_event_id') or self.selected_deadline.google_event_id != google_event_id:
                                self.selected_deadline.google_event_id = google_event_id
                                self.db_manager.update_deadline(self.selected_deadline)
                            sync_message = " (Sincronizzata con Google Calendar)"
                            logger.info(f"Deadline synced to Google Calendar: {google_event_id}")
                        else:
                            sync_message = " (Errore sincronizzazione Google Calendar)"
                            sync_success = False
                    except Exception as e:
                        logger.warning(f"Failed to sync deadline to Google Calendar: {e}")
                        sync_message = " (Errore sincronizzazione Google Calendar)"
                        sync_success = False

                action = "modificata"
                message = f"Scadenza '{data['title']}' modificata con successo!{sync_message}"
            else:
                # Crea nuova scadenza
                new_deadline = Deadline(
                    title=data['title'],
                    project_id=project_id,
                    priority=Priority(data['priority']),
                    due_date=due_date,
                    description=data['description'],
                    recurrence_type=recurrence_type,
                    recurrence_interval=recurrence_interval,
                    recurrence_end_date=recurrence_end_date,
                    is_recurring=recurrence_type != RecurrenceType.NONE,
                    email_notifications=email_notifications,
                    alert_days_before=alert_days_before,
                    sync_to_google=sync_to_google,
                    status=DeadlineStatus.PENDING
                )
                
                success = self.db_manager.create_deadline(new_deadline)

                # Enhanced Google Calendar sync with improved feedback
                sync_success = True
                sync_message = ""
                if success and sync_to_google and self._should_auto_sync():
                    try:
                        google_event_id = self.google_service.sync_deadline_to_google(new_deadline)
                        if google_event_id:
                            # The service already updates the deadline object with Google event ID
                            # Now save it to the database
                            if not hasattr(new_deadline, 'google_event_id') or new_deadline.google_event_id != google_event_id:
                                new_deadline.google_event_id = google_event_id
                                self.db_manager.update_deadline(new_deadline)
                            sync_message = " (Sincronizzata con Google Calendar)"
                            logger.info(f"New deadline synced to Google Calendar: {google_event_id}")
                        else:
                            sync_message = " (Errore sincronizzazione Google Calendar)"
                            sync_success = False
                    except Exception as e:
                        logger.warning(f"Failed to sync new deadline to Google Calendar: {e}")
                        sync_message = " (Errore sincronizzazione Google Calendar)"
                        sync_success = False

                # Se è ricorrente, crea le scadenze future
                if success and recurrence_type != RecurrenceType.NONE:
                    self._create_recurring_deadlines(new_deadline)

                action = "creata"
                message = f"Scadenza '{data['title']}' creata con successo!{sync_message}"
            
            if success:
                logger.info(f"Scadenza {action} con successo")
                
                # Controlla se serve creare alert
                self.app.alert_service.check_and_create_alerts()
                
                self._close_form()
                self.refresh_data()
                
                # Mostra messaggio di successo
                self._show_success_dialog("Successo", message)
                
                # Notifica di sistema (Windows)
                self._show_system_notification("Agevolami PM", message)
                
            else:
                self._show_error_dialog("Errore", "Errore durante il salvataggio della scadenza")
                
        except Exception as e:
            logger.error(f"Errore salvataggio scadenza: {e}")
            self._show_error_dialog("Errore", f"Errore imprevisto: {str(e)}")
    
    def _create_recurring_deadlines(self, base_deadline: Deadline):
        """Crea le scadenze ricorrenti"""
        try:
            current_date = base_deadline.due_date
            end_date = base_deadline.recurrence_end_date or (current_date + timedelta(days=365*2))  # Default 2 anni
            created_count = 0
            max_occurrences = 100  # Limite di sicurezza
            
            while current_date < end_date and created_count < max_occurrences:
                # Calcola la prossima data
                if base_deadline.recurrence_type == RecurrenceType.DAILY:
                    current_date += timedelta(days=base_deadline.recurrence_interval)
                elif base_deadline.recurrence_type == RecurrenceType.WEEKLY:
                    current_date += timedelta(weeks=base_deadline.recurrence_interval)
                elif base_deadline.recurrence_type == RecurrenceType.MONTHLY:
                    # Gestione mesi
                    month = current_date.month
                    year = current_date.year
                    month += base_deadline.recurrence_interval
                    while month > 12:
                        month -= 12
                        year += 1
                    try:
                        current_date = current_date.replace(year=year, month=month)
                    except ValueError:
                        # Gestisce il caso del 29/30/31 in mesi che non li hanno
                        import calendar
                        last_day = calendar.monthrange(year, month)[1]
                        current_date = current_date.replace(year=year, month=month, day=min(current_date.day, last_day))
                elif base_deadline.recurrence_type == RecurrenceType.QUARTERLY:
                    # Ogni 3 mesi
                    month = current_date.month
                    year = current_date.year
                    month += 3 * base_deadline.recurrence_interval
                    while month > 12:
                        month -= 12
                        year += 1
                    try:
                        current_date = current_date.replace(year=year, month=month)
                    except ValueError:
                        import calendar
                        last_day = calendar.monthrange(year, month)[1]
                        current_date = current_date.replace(year=year, month=month, day=min(current_date.day, last_day))
                elif base_deadline.recurrence_type == RecurrenceType.YEARLY:
                    try:
                        current_date = current_date.replace(year=current_date.year + base_deadline.recurrence_interval)
                    except ValueError:
                        # Gestisce il 29 febbraio negli anni non bisestili
                        current_date = current_date.replace(year=current_date.year + base_deadline.recurrence_interval, day=28)
                
                if current_date >= end_date:
                    break
                
                # Crea la nuova scadenza
                recurring_deadline = Deadline(
                    title=base_deadline.title,
                    description=base_deadline.description,
                    due_date=current_date,
                    priority=base_deadline.priority,
                    project_id=base_deadline.project_id,
                    recurrence_type=RecurrenceType.NONE,  # Le scadenze generate non sono ricorrenti
                    parent_deadline_id=base_deadline.id,
                    email_notifications=base_deadline.email_notifications,
                    alert_days_before=base_deadline.alert_days_before,
                    status=DeadlineStatus.PENDING
                )
                
                if self.db_manager.create_deadline(recurring_deadline):
                    created_count += 1
                else:
                    logger.error(f"Errore creazione scadenza ricorrente per data {current_date}")
                    break
            
            if created_count > 0:
                logger.info(f"Create {created_count} scadenze ricorrenti")
                self._show_success_dialog("Ricorrenze", f"Create {created_count} scadenze ricorrenti")
            
        except Exception as e:
            logger.error(f"Errore creazione scadenze ricorrenti: {e}")
            self._show_error_dialog("Errore", f"Errore nella creazione delle ricorrenze: {str(e)}")
    
    def refresh_data(self):
        """Aggiorna i dati delle scadenze con caching"""
        try:
            # Load data using cache for better performance
            from core.services.data_cache_manager import get_data_cache
            data_cache = get_data_cache(self.db_manager)

            self.current_deadlines = data_cache.get_all_deadlines()
            self.current_projects = {p.id: p for p in data_cache.get_all_projects()}
            self.current_clients = {c.id: c for c in data_cache.get_all_clients()}

            # Refresh Google Calendar authentication status
            self._refresh_google_calendar_status()

            logger.info(f"Caricate {len(self.current_deadlines)} scadenze, {len(self.current_projects)} progetti, {len(self.current_clients)} clienti")

            self._filter_deadlines()
            self._update_deadlines_list()
            logger.info(f"Caricate {len(self.current_deadlines)} scadenze")
        except Exception as e:
            logger.error(f"Errore caricamento scadenze: {e}")
            self.current_deadlines = []
            self.current_projects = {}
            self.current_clients = {}
            self._update_deadlines_list()
    
    def refresh(self):
        """Aggiorna la vista scadenze"""
        self.refresh_data()
    
    def cleanup(self):
        """Pulisce le risorse quando la vista viene distrutta"""
        if self.search_timer:
            self.search_timer.cancel()
            self.search_timer = None
    
    def _show_success_dialog(self, title: str, message: str):
        """Mostra un dialog di successo"""
        def close_dialog(e):
            self.app.page.close(success_dialog)
        
        success_dialog = ft.AlertDialog(
            title=ft.Text(title, color=ft.Colors.GREEN),
            content=ft.Text(message),
            actions=[
                ft.TextButton(
                    "OK",
                    on_click=close_dialog
                )
            ]
        )
        self.app.page.open(success_dialog)
    
    def _show_error_dialog(self, title: str, message: str):
        """Mostra un dialog di errore"""
        def close_dialog(e):
            self.app.page.close(error_dialog)
        
        error_dialog = ft.AlertDialog(
            title=ft.Text(title, color=ft.Colors.RED),
            content=ft.Text(message),
            actions=[
                ft.TextButton(
                    "OK",
                    on_click=close_dialog
                )
            ]
        )
        self.app.page.open(error_dialog)
    
    def _show_system_notification(self, title: str, message: str):
        """Mostra una notifica di sistema (Windows)"""
        try:
            # Use the new Windows integration if available
            if hasattr(self.app, 'windows_integration'):
                success = self.app.windows_integration.send_notification(
                    title, message, "normal"
                )
                if success:
                    return
            
            # Fallback to original implementation
            import platform
            if platform.system() == "Windows":
                try:
                    # Prova prima con plyer che è più stabile
                    from plyer import notification
                    notification.notify(
                        title=title,
                        message=message,
                        timeout=5
                    )
                except ImportError:
                    try:
                        # Fallback a win10toast con gestione errori migliorata
                        from win10toast import ToastNotifier
                        toaster = ToastNotifier()
                        # Usa show_toast con parametri più sicuri
                        toaster.show_toast(
                            title,
                            message,
                            duration=3,
                            threaded=True,
                            icon_path=None
                        )
                    except (ImportError, AttributeError, Exception) as toast_error:
                        # Se win10toast fallisce, usa notifica console
                        logger.info(f"Notifica: {title} - {message}")
                        logger.debug(f"Toast notification error: {toast_error}")
                except Exception as plyer_error:
                    # Se plyer fallisce, prova win10toast o fallback
                    logger.debug(f"Plyer notification error: {plyer_error}")
                    try:
                        from win10toast import ToastNotifier
                        toaster = ToastNotifier()
                        toaster.show_toast(
                            title,
                            message,
                            duration=3,
                            threaded=True,
                            icon_path=None
                        )
                    except Exception:
                        logger.info(f"Notifica: {title} - {message}")
            else:
                # Per altri OS, usa solo plyer
                try:
                    from plyer import notification
                    notification.notify(
                        title=title,
                        message=message,
                        timeout=5
                    )
                except ImportError:
                    logger.info(f"Notifica: {title} - {message}")
        except Exception as e:
            logger.error(f"Errore notifica di sistema: {e}")
            # Fallback finale: log della notifica
            logger.info(f"Notifica: {title} - {message}")
    
    def _show_export_dialog(self):
        """Mostra il dialog per l'esportazione delle scadenze"""
        if not PDF_AVAILABLE:
            self._show_error_dialog("Errore", "Libreria PDF non disponibile. Installa reportlab: pip install reportlab")
            return
        
        # Opzioni di esportazione
        export_type = ft.Ref[ft.RadioGroup]()
        include_completed = ft.Ref[ft.Checkbox]()
        filter_by_project = ft.Ref[ft.Checkbox]()
        project_dropdown = ft.Ref[ft.Dropdown]()
        email_export = ft.Ref[ft.Checkbox]()
        email_address = ft.Ref[ft.TextField]()
        
        # Popola progetti per il filtro
        project_options = [ft.dropdown.Option(key="all", text="Tutti i progetti")]
        for project in self.current_projects.values():
            project_options.append(
                ft.dropdown.Option(key=str(project.id), text=project.name)
            )
        
        def on_project_filter_change(e):
            project_dropdown.current.disabled = not filter_by_project.current.value
            self.app.page.update()
        
        def on_email_change(e):
            email_address.current.disabled = not email_export.current.value
            self.app.page.update()
        
        def export_deadlines(e):
            self.app.page.close(export_dialog)
            
            try:
                # Raccogli opzioni
                export_option = export_type.current.value
                include_completed_deadlines = include_completed.current.value
                filter_project = filter_by_project.current.value
                selected_project_id = project_dropdown.current.value if filter_project else None
                send_email = email_export.current.value
                recipient_email = email_address.current.value if send_email else None
                
                # Filtra scadenze in base alle opzioni
                filtered_deadlines = self._filter_deadlines_for_export(
                    export_option, include_completed_deadlines, selected_project_id
                )
                
                if not filtered_deadlines:
                    self._show_error_dialog("Nessuna scadenza", "Nessuna scadenza trovata con i filtri selezionati.")
                    return
                
                # Genera PDF
                pdf_path = self._generate_deadlines_pdf(filtered_deadlines, export_option, selected_project_id)
                
                if send_email and recipient_email:
                    # Invia via email
                    self._send_export_email(pdf_path, recipient_email, export_option)
                else:
                    # Show success dialog with sharing options
                    self._show_export_success_dialog(pdf_path, "PDF", export_option)
                
                self._show_success_dialog("Esportazione completata", 
                    f"PDF generato con {len(filtered_deadlines)} scadenze.")
                
            except Exception as ex:
                logger.error(f"Errore durante l'esportazione: {ex}")
                self._show_error_dialog("Errore", f"Errore durante l'esportazione: {str(ex)}")
        
        def close_dialog(e):
            self.app.page.close(export_dialog)
        
        export_dialog = ft.AlertDialog(
            modal=True,
            title=ft.Row([
                ft.Icon(ft.Icons.DOWNLOAD, color=ft.Colors.GREEN_600),
                ft.Text("Esporta Scadenze", weight=ft.FontWeight.BOLD, size=18)
            ], alignment=ft.MainAxisAlignment.START),
            content=ft.Container(
                content=ft.Column([
                    ft.Container(
                        content=ft.Text(
                            "Seleziona le opzioni di esportazione:", 
                            size=14, 
                            weight=ft.FontWeight.W_500,
                            color=ft.Colors.BLUE_GREY_700
                        ),
                        margin=ft.margin.only(bottom=10)
                    ),
                    
                    # Scrollable content
                    ft.Container(
                        content=ft.Column([
                            # Tipo di esportazione
                            ft.Container(
                                content=ft.Column([
                                    ft.Row([
                                        ft.Icon(ft.Icons.DATE_RANGE, size=20, color=ft.Colors.BLUE_600),
                                        ft.Text("Periodo:", weight=ft.FontWeight.BOLD, size=16)
                                    ]),
                                    ft.Container(
                                        content=ft.RadioGroup(
                                            ref=export_type,
                                            content=ft.Column([
                                                ft.Radio(
                                                    value="upcoming", 
                                                    label="Prossime 15 giorni (incluse scadute)",
                                                    label_style=ft.TextStyle(size=14)
                                                ),
                                                ft.Radio(
                                                    value="year", 
                                                    label="Tutto l'anno corrente",
                                                    label_style=ft.TextStyle(size=14)
                                                ),
                                                ft.Radio(
                                                    value="all", 
                                                    label="Tutte le scadenze",
                                                    label_style=ft.TextStyle(size=14)
                                                )
                                            ], spacing=8),
                                            value="upcoming"
                                        ),
                                        padding=ft.padding.only(left=20)
                                    )
                                ], spacing=8),
                                padding=ft.padding.all(10),
                                bgcolor=ft.Colors.BLUE_50,
                                border_radius=8
                            ),
                            
                            # Opzioni aggiuntive
                            ft.Container(
                                content=ft.Column([
                                    ft.Row([
                                        ft.Icon(ft.Icons.TUNE, size=20, color=ft.Colors.ORANGE_600),
                                        ft.Text("Opzioni:", weight=ft.FontWeight.BOLD, size=16)
                                    ]),
                                    ft.Container(
                                        content=ft.Column([
                                            ft.Checkbox(
                                                ref=include_completed,
                                                label="Includi scadenze completate",
                                                value=False,
                                                label_style=ft.TextStyle(size=14)
                                            ),
                                            ft.Checkbox(
                                                ref=filter_by_project,
                                                label="Filtra per progetto",
                                                value=False,
                                                on_change=on_project_filter_change,
                                                label_style=ft.TextStyle(size=14)
                                            ),
                                            ft.Container(
                                                content=ft.Dropdown(
                                                    ref=project_dropdown,
                                                    label="Progetto",
                                                    options=project_options,
                                                    value="all",
                                                    disabled=True,
                                                    text_size=14
                                                ),
                                                padding=ft.padding.only(left=20)
                                            )
                                        ], spacing=8),
                                        padding=ft.padding.only(left=20)
                                    )
                                ], spacing=8),
                                padding=ft.padding.all(10),
                                bgcolor=ft.Colors.ORANGE_50,
                                border_radius=8
                            ),
                            
                            # Opzioni email
                            ft.Container(
                                content=ft.Column([
                                    ft.Row([
                                        ft.Icon(ft.Icons.EMAIL, size=20, color=ft.Colors.GREEN_600),
                                        ft.Text("Invio:", weight=ft.FontWeight.BOLD, size=16)
                                    ]),
                                    ft.Container(
                                        content=ft.Column([
                                            ft.Checkbox(
                                                ref=email_export,
                                                label="Invia via email",
                                                value=False,
                                                on_change=on_email_change,
                                                label_style=ft.TextStyle(size=14)
                                            ),
                                            ft.Container(
                                                content=ft.TextField(
                                                    ref=email_address,
                                                    label="Indirizzo email destinatario",
                                                    hint_text="<EMAIL>",
                                                    disabled=True,
                                                    text_size=14,
                                                    prefix_icon=ft.Icons.ALTERNATE_EMAIL
                                                ),
                                                padding=ft.padding.only(left=20)
                                            )
                                        ], spacing=8),
                                        padding=ft.padding.only(left=20)
                                    )
                                ], spacing=8),
                                padding=ft.padding.all(10),
                                bgcolor=ft.Colors.GREEN_50,
                                border_radius=8
                            )
                        ], spacing=15, scroll=ft.ScrollMode.AUTO),
                        height=400,
                        padding=ft.padding.all(5)
                    )
                ], spacing=0),
                width=450,
                height=500
            ),
            actions=[
                ft.Container(
                    content=ft.Row([
                        ft.TextButton(
                            "Annulla",
                            on_click=close_dialog,
                            style=ft.ButtonStyle(
                                color=ft.Colors.GREY_600,
                                text_style=ft.TextStyle(size=14, weight=ft.FontWeight.W_500)
                            )
                        ),
                        ft.ElevatedButton(
                            "Esporta PDF",
                            icon=ft.Icons.PICTURE_AS_PDF,
                            on_click=export_deadlines,
                            style=ft.ButtonStyle(
                                bgcolor=ft.Colors.GREEN_600,
                                color=ft.Colors.WHITE,
                                text_style=ft.TextStyle(size=14, weight=ft.FontWeight.BOLD),
                                elevation=3,
                                shape=ft.RoundedRectangleBorder(radius=8)
                            )
                        )
                    ], alignment=ft.MainAxisAlignment.END, spacing=10),
                    padding=ft.padding.symmetric(horizontal=10, vertical=5)
                )
            ],
            actions_alignment=ft.MainAxisAlignment.END
        )
        
        self.app.page.open(export_dialog)
    
    def _filter_deadlines_for_export(self, export_option: str, include_completed: bool, project_id: str = None) -> List[Deadline]:
        """Filtra le scadenze in base alle opzioni di esportazione"""
        today = date.today()
        filtered = []
        
        for deadline in self.current_deadlines:
            # Filtra per stato completamento
            if not include_completed and deadline.status == DeadlineStatus.COMPLETED:
                continue
            
            # Filtra per progetto
            if project_id and project_id != "all" and str(deadline.project_id) != project_id:
                continue
            
            # Filtra per periodo
            if export_option == "upcoming":
                # Prossimi 15 giorni + scadute
                if deadline.due_date > today + timedelta(days=15):
                    continue
            elif export_option == "year":
                # Anno corrente
                if deadline.due_date.year != today.year:
                    continue
            # "all" non ha filtri temporali
            
            filtered.append(deadline)
        
        # Ordina per data di scadenza
        filtered.sort(key=lambda d: d.due_date)
        return filtered
    
    def _generate_deadlines_pdf(self, deadlines: List[Deadline], export_option: str, project_id: str = None) -> str:
        """Genera un PDF con le scadenze filtrate"""
        # Crea file temporaneo
        temp_dir = tempfile.gettempdir()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"scadenze_export_{timestamp}.pdf"
        pdf_path = os.path.join(temp_dir, filename)
        
        # Crea documento PDF
        doc = SimpleDocTemplate(pdf_path, pagesize=A4)
        styles = getSampleStyleSheet()
        story = []
        
        # Stile personalizzato per il titolo
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.darkblue
        )
        
        # Titolo
        period_text = {
            "upcoming": "Prossime 15 giorni",
            "year": f"Anno {date.today().year}",
            "all": "Tutte le scadenze"
        }.get(export_option, "Scadenze")
        
        project_text = ""
        if project_id and project_id != "all":
            project = next((p for p in self.current_projects.values() if str(p.id) == project_id), None)
            if project:
                project_text = f" - {project.name}"
        
        title = f"Report Scadenze - {period_text}{project_text}"
        story.append(Paragraph(title, title_style))
        
        # Informazioni generali
        info_text = f"Generato il: {datetime.now().strftime('%d/%m/%Y alle %H:%M')}<br/>"
        info_text += f"Totale scadenze: {len(deadlines)}"
        story.append(Paragraph(info_text, styles['Normal']))
        story.append(Spacer(1, 20))
        
        # Tabella scadenze
        if deadlines:
            # Header tabella
            data = [['Titolo', 'Progetto', 'Cliente', 'Scadenza', 'Priorità', 'Stato']]
            
            # Dati scadenze
            for deadline in deadlines:
                project_name = "N/A"
                client_name = "N/A"
                
                # Trova progetto
                if deadline.project_id:
                    project = next((p for p in self.current_projects.values() if p.id == deadline.project_id), None)
                    if project:
                        project_name = project.name
                        # Trova cliente
                        client = next((c for c in self.current_clients.values() if c.id == project.client_id), None)
                        if client:
                            client_name = client.business_name or client.name
                
                priority_text = {
                    Priority.LOW: "Bassa",
                    Priority.MEDIUM: "Media", 
                    Priority.HIGH: "Alta",
                    Priority.CRITICAL: "Critica"
                }.get(deadline.priority, "N/A")
                
                status_text = {
                    DeadlineStatus.PENDING: "In attesa",
                    DeadlineStatus.COMPLETED: "Completata",
                    DeadlineStatus.OVERDUE: "Scaduta",
                    DeadlineStatus.CANCELLED: "Annullata"
                }.get(deadline.status, "N/A")
                
                data.append([
                    deadline.title,
                    project_name,
                    client_name,
                    deadline.due_date.strftime('%d/%m/%Y'),
                    priority_text,
                    status_text
                ])
            
            # Crea tabella
            table = Table(data, colWidths=[2*inch, 1.5*inch, 1.5*inch, 1*inch, 0.8*inch, 1*inch])
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 1), (-1, -1), 8),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(table)
        else:
            story.append(Paragraph("Nessuna scadenza trovata con i filtri selezionati.", styles['Normal']))
        
        # Genera PDF
        doc.build(story)
        logger.info(f"PDF generato: {pdf_path}")
        return pdf_path
    
    def _send_export_email(self, pdf_path: str, recipient_email: str, export_option: str):
        """Invia il PDF via email"""
        try:
            email_service = EmailService(self.app.config)
            
            period_text = {
                "upcoming": "prossime 15 giorni",
                "year": f"anno {date.today().year}",
                "all": "tutte le scadenze"
            }.get(export_option, "scadenze")
            
            subject = f"Report Scadenze - {period_text.title()}"
            body = f"""Gentile utente,

In allegato trovi il report delle scadenze per {period_text}.

Il report è stato generato automaticamente dal sistema Agevolami PM.

Cordiali saluti,
Team Agevolami"""
            
            html_body = f"""<html><body>
<h2>Report Scadenze</h2>
<p>Gentile utente,</p>
<p>In allegato trovi il report delle scadenze per <strong>{period_text}</strong>.</p>
<p>Il report è stato generato automaticamente dal sistema Agevolami PM.</p>
<br>
<p>Cordiali saluti,<br>Team Agevolami</p>
</body></html>"""
            
            success = email_service.send_email(
                to_email=recipient_email,
                subject=subject,
                body=body,
                html_body=html_body,
                attachments=[pdf_path]
            )
            
            if success:
                logger.info(f"Report inviato via email a: {recipient_email}")
            else:
                raise Exception("Invio email fallito")
                
        except Exception as e:
            logger.error(f"Errore invio email: {e}")
            raise e
    
    def _show_export_success_dialog(self, filename: str, format_type: str, export_option: str):
        """Show export success dialog with sharing options including SMTP"""
        import os
        import subprocess
        import urllib.parse
        from datetime import datetime
        
        def close_dialog(e):
            self.app.page.close(dialog)
        
        def open_file_location(e):
            """Open file location in file explorer"""
            try:
                folder_path = os.path.dirname(filename)
                if os.name == 'nt':  # Windows
                    try:
                        subprocess.run(['explorer', '/select,', filename], check=False)
                    except Exception:
                        subprocess.run(['explorer', folder_path], check=False)
                elif os.name == 'posix':  # macOS/Linux
                    if os.uname().sysname == 'Darwin':  # macOS
                        subprocess.run(['open', '-R', filename], check=False)
                    else:  # Linux
                        subprocess.run(['xdg-open', folder_path], check=False)
                close_dialog(e)
            except Exception as ex:
                logger.error(f"Error opening file location: {ex}")
                self._show_error_dialog("Errore", f"Impossibile aprire la cartella: {str(ex)}")
        
        def copy_file_path(e):
            """Copy file path to clipboard"""
            try:
                import pyperclip
                pyperclip.copy(filename)
                self._show_success_dialog("Successo", "Percorso file copiato negli appunti")
                close_dialog(e)
            except ImportError:
                self._show_error_dialog("Errore", "Installa 'pyperclip' per copiare il percorso")
            except Exception as ex:
                logger.error(f"Error copying to clipboard: {ex}")
        
        def share_via_email(e):
            """Share file via system email client"""
            try:
                if os.name == 'nt':  # Windows
                    try:
                        import win32api
                        import win32con
                        
                        subject = f"Export Scadenze - {format_type}"
                        body = f"Ciao,\n\nTi invio l'export delle scadenze da Agevolami PM.\n\nFile allegato: {os.path.basename(filename)}\nGenerato: {datetime.now().strftime('%d/%m/%Y alle %H:%M')}\n\nCordiali saluti"
                        
                        win32api.ShellExecute(
                            0,
                            'open',
                            'mailto:',
                            f'subject={subject}&body={body}&attach={filename}',
                            '',
                            win32con.SW_SHOWNORMAL
                        )
                        
                    except ImportError:
                        try:
                            subprocess.run(['explorer', '/select,', filename], check=False)
                        except Exception:
                            subprocess.run(['explorer', os.path.dirname(filename)], check=False)
                        subject = f"Export Scadenze - {format_type}"
                        body = f"Ciao,\n\nTi invio l'export delle scadenze da Agevolami PM.\n\nFile: {os.path.basename(filename)}\nGenerato: {datetime.now().strftime('%d/%m/%Y alle %H:%M')}\n\nCordiali saluti"
                        mailto_url = f"mailto:?subject={urllib.parse.quote(subject)}&body={urllib.parse.quote(body)}"
                        os.startfile(mailto_url)
                
                else:  # macOS/Linux
                    folder_path = os.path.dirname(filename)
                    subject = f"Export Scadenze - {format_type}"
                    body = f"Ciao,\n\nTi invio l'export delle scadenze da Agevolami PM.\n\nFile: {os.path.basename(filename)}\nGenerato: {datetime.now().strftime('%d/%m/%Y alle %H:%M')}\n\nCordiali saluti"
                    mailto_url = f"mailto:?subject={urllib.parse.quote(subject)}&body={urllib.parse.quote(body)}"
                    
                    if os.uname().sysname == 'Darwin':  # macOS
                        subprocess.run(['open', '-R', filename], check=True)
                        subprocess.run(['open', mailto_url])
                    else:  # Linux
                        subprocess.run(['xdg-open', folder_path], check=True)
                        subprocess.run(['xdg-open', mailto_url])
                
                close_dialog(e)
                
            except Exception as ex:
                logger.error(f"Error opening email client: {ex}")
                self._show_error_dialog("Errore", f"Errore apertura email: {str(ex)}")
        
        # Get file info
        file_size = os.path.getsize(filename) if os.path.exists(filename) else 0
        file_size_mb = file_size / (1024 * 1024)
        
        period_text = {
            "upcoming": "Prossime 15 giorni",
            "year": f"Anno {date.today().year}",
            "all": "Tutte le scadenze"
        }.get(export_option, "Scadenze")
        
        # Create dialog
        dialog = ft.AlertDialog(
            title=ft.Row([
                ft.Icon(ft.Icons.CHECK_CIRCLE, color=ft.Colors.GREEN_600, size=24),
                ft.Text("Export Completato!", weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN_600)
            ], spacing=8),
            content=ft.Container(
                content=ft.Column([
                    # File info
                    ft.Container(
                        content=ft.Column([
                            ft.Row([
                                ft.Icon(ft.Icons.DESCRIPTION, size=16, color=ft.Colors.BLUE_600),
                                ft.Text("Informazioni File", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_600)
                            ], spacing=6),
                            ft.Text(f"Nome: {os.path.basename(filename)}", size=12, color=ft.Colors.GREY_700),
                            ft.Text(f"Formato: {format_type}", size=12, color=ft.Colors.GREY_700),
                            ft.Text(f"Dimensione: {file_size_mb:.2f} MB", size=12, color=ft.Colors.GREY_700),
                            ft.Text(f"Contenuto: {period_text}", size=12, color=ft.Colors.GREY_700),
                            ft.Text(f"Percorso: {filename}", size=10, color=ft.Colors.GREY_500, selectable=True)
                        ], spacing=4),
                        padding=ft.padding.all(12),
                        bgcolor=ft.Colors.BLUE_50,
                        border_radius=8,
                        border=ft.border.all(1, ft.Colors.BLUE_200)
                    ),
                    
                    ft.Container(height=10),
                    
                    # Action buttons
                    ft.Text("Azioni Disponibili:", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_700),
                    
                    ft.Row([
                        ft.ElevatedButton(
                            content=ft.Row([
                                ft.Icon(ft.Icons.FOLDER_OPEN, size=16),
                                ft.Text("Apri Cartella", size=12)
                            ], spacing=4, tight=True),
                            on_click=open_file_location,
                            bgcolor=ft.Colors.BLUE_600,
                            color=ft.Colors.WHITE
                        ),
                        ft.ElevatedButton(
                            content=ft.Row([
                                ft.Icon(ft.Icons.COPY, size=16),
                                ft.Text("Copia Percorso", size=12)
                            ], spacing=4, tight=True),
                            on_click=copy_file_path,
                            bgcolor=ft.Colors.GREY_600,
                            color=ft.Colors.WHITE
                        )
                    ], spacing=8),
                    
                    ft.Row([
                        ft.ElevatedButton(
                            content=ft.Row([
                                ft.Icon(ft.Icons.EMAIL, size=16),
                                ft.Text("Condividi Email", size=12)
                            ], spacing=4, tight=True),
                            on_click=share_via_email,
                            bgcolor=ft.Colors.GREEN_600,
                            color=ft.Colors.WHITE
                        ),
                        ft.ElevatedButton(
                            content=ft.Row([
                                ft.Icon(ft.Icons.SEND, size=16),
                                ft.Text("SMTP Email", size=12)
                            ], spacing=4, tight=True),
                            on_click=lambda e: self._show_smtp_email_dialog(filename, format_type, export_option),
                            bgcolor=ft.Colors.INDIGO_600,
                            color=ft.Colors.WHITE
                        )
                    ], spacing=8)
                ], spacing=8),
                width=500
            ),
            actions=[
                ft.TextButton("Chiudi", on_click=close_dialog)
            ]
        )
        
        self.app.page.open(dialog)
    
    def _show_smtp_email_dialog(self, filename: str, format_type: str, export_option: str):
        """Show SMTP email dialog for direct email sending"""
        import os
        from datetime import datetime
        
        # References for form fields
        recipient_ref = ft.Ref[ft.TextField]()
        subject_ref = ft.Ref[ft.TextField]()
        message_ref = ft.Ref[ft.TextField]()
        
        def close_dialog(e):
            self.app.page.close(smtp_dialog)
        
        def send_via_smtp(e):
            """Send email via SMTP with attachment"""
            try:
                recipient = recipient_ref.current.value
                subject = subject_ref.current.value
                message = message_ref.current.value
                
                # Basic validation
                if not recipient or not subject:
                    self._show_error_dialog("Errore", "Destinatario e oggetto sono obbligatori")
                    return
                
                # Validate email format
                import re
                email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
                if not re.match(email_pattern, recipient):
                    self._show_error_dialog("Errore", "Formato email non valido")
                    return
                
                # Load current settings
                import json
                from pathlib import Path
                
                settings_file = Path('data/settings.json')
                email_settings = {}
                
                if settings_file.exists():
                    try:
                        with open(settings_file, 'r', encoding='utf-8') as f:
                            settings = json.load(f)
                            email_settings = settings.get('email', {})
                    except Exception as e:
                        logger.error(f"Error loading email settings: {e}")
                
                # Check if SMTP is configured
                if not email_settings.get('server') or not email_settings.get('username'):
                    self._show_error_dialog("Errore", "SMTP non configurato. Vai alle Impostazioni per configurarlo.")
                    return
                
                # Initialize email service
                from core.config.app_config import AppConfig
                config = AppConfig()
                email_service = EmailService(config)
                
                # Update email service with current settings
                email_service.smtp_config.update({
                    'smtp_server': email_settings.get('server', ''),
                    'smtp_port': email_settings.get('port', 587),
                    'smtp_username': email_settings.get('username', ''),
                    'smtp_password': email_settings.get('password', ''),
                    'smtp_use_tls': email_settings.get('use_tls', True),
                    'from_name': email_settings.get('sender_name', 'Agevolami PM'),
                    'from_email': email_settings.get('sender_email', email_settings.get('username', '')),
                    'enabled': True
                })
                
                # Test connection first
                if not email_service.test_connection():
                    self._show_error_dialog("Errore", "Impossibile connettersi al server SMTP. Verifica le impostazioni.")
                    return
                
                # Send email with attachment
                success = email_service.send_email_with_attachment(
                    to_email=recipient,
                    subject=subject,
                    body=message,
                    attachment_path=filename
                )
                
                if success:
                    # Close dialog and show success
                    close_dialog(e)
                    self._show_success_dialog("Successo", f"Email inviata con successo a {recipient}")
                    logger.info(f"SMTP email sent successfully to {recipient}")
                else:
                    self._show_error_dialog("Errore", "Errore durante l'invio dell'email")
                    
            except Exception as ex:
                logger.error(f"SMTP email error: {ex}")
                self._show_error_dialog("Errore", f"Errore SMTP: {str(ex)}")
        
        # Get file info
        file_size = os.path.getsize(filename) if os.path.exists(filename) else 0
        file_size_mb = file_size / (1024 * 1024)
        
        period_text = {
            "upcoming": "prossime 15 giorni",
            "year": f"anno {date.today().year}",
            "all": "tutte le scadenze"
        }.get(export_option, "scadenze")
        
        # Create default email content
        default_subject = f"Export Scadenze - {period_text.title()} - {datetime.now().strftime('%d/%m/%Y')}"
        default_message = f"""Ciao,

Ti invio l'export delle scadenze per {period_text} da Agevolami PM.

Dettagli file:
- Nome: {os.path.basename(filename)}
- Formato: {format_type}
- Dimensione: {file_size_mb:.2f} MB
- Contenuto: {period_text.title()}
- Generato: {datetime.now().strftime('%d/%m/%Y alle %H:%M')}

Il file è allegato a questa email.

Cordiali saluti,
Agevolami PM"""
        
        # Create dialog
        smtp_dialog = ft.AlertDialog(
            title=ft.Row([
                ft.Icon(ft.Icons.SEND, color=ft.Colors.INDIGO_600, size=24),
                ft.Text("Invia Email SMTP", weight=ft.FontWeight.BOLD, color=ft.Colors.INDIGO_600)
            ], spacing=8),
            content=ft.Container(
                content=ft.Column([
                    # File info
                    ft.Container(
                        content=ft.Column([
                            ft.Row([
                                ft.Icon(ft.Icons.ATTACH_FILE, size=16, color=ft.Colors.BLUE_600),
                                ft.Text("File Allegato", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_600)
                            ], spacing=6),
                            ft.Text(f"Nome: {os.path.basename(filename)}", size=12, color=ft.Colors.GREY_700),
                            ft.Text(f"Formato: {format_type}", size=12, color=ft.Colors.GREY_700),
                            ft.Text(f"Dimensione: {file_size_mb:.2f} MB", size=12, color=ft.Colors.GREY_700),
                            ft.Text(f"Contenuto: {period_text.title()}", size=12, color=ft.Colors.GREY_700),
                        ], spacing=4),
                        padding=ft.padding.all(12),
                        bgcolor=ft.Colors.BLUE_50,
                        border_radius=8,
                        border=ft.border.all(1, ft.Colors.BLUE_200)
                    ),
                    
                    ft.Container(height=10),
                    
                    # Email form
                    ft.Text("Componi Email:", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_700),
                    
                    ft.TextField(
                        ref=recipient_ref,
                        label="Destinatario Email *",
                        hint_text="<EMAIL>",
                        prefix_icon=ft.Icons.PERSON,
                        text_size=14,
                        border_color=ft.Colors.INDIGO_200,
                        focused_border_color=ft.Colors.INDIGO_600
                    ),
                    
                    ft.TextField(
                        ref=subject_ref,
                        label="Oggetto *",
                        value=default_subject,
                        prefix_icon=ft.Icons.SUBJECT,
                        text_size=14,
                        border_color=ft.Colors.INDIGO_200,
                        focused_border_color=ft.Colors.INDIGO_600
                    ),
                    
                    ft.TextField(
                        ref=message_ref,
                        label="Messaggio",
                        value=default_message,
                        prefix_icon=ft.Icons.MESSAGE,
                        text_size=14,
                        multiline=True,
                        max_lines=8,
                        border_color=ft.Colors.INDIGO_200,
                        focused_border_color=ft.Colors.INDIGO_600
                    ),
                    
                    # Info note
                    ft.Container(
                        content=ft.Row([
                            ft.Icon(ft.Icons.INFO, size=16, color=ft.Colors.ORANGE_600),
                            ft.Text("Utilizza le impostazioni SMTP configurate nell'applicazione", 
                                   size=12, color=ft.Colors.ORANGE_600, expand=True)
                        ], spacing=6),
                        padding=ft.padding.all(8),
                        bgcolor=ft.Colors.ORANGE_50,
                        border_radius=6,
                        border=ft.border.all(1, ft.Colors.ORANGE_200)
                    )
                ], spacing=8, scroll=ft.ScrollMode.AUTO),
                width=500,
                height=500
            ),
            actions=[
                ft.Row([
                    ft.TextButton(
                        "Annulla",
                        on_click=close_dialog,
                        style=ft.ButtonStyle(color=ft.Colors.GREY_600)
                    ),
                    ft.ElevatedButton(
                        content=ft.Row([
                            ft.Icon(ft.Icons.SEND, size=16),
                            ft.Text("Invia Email", size=12)
                        ], spacing=4, tight=True),
                        on_click=send_via_smtp,
                        bgcolor=ft.Colors.INDIGO_600,
                        color=ft.Colors.WHITE
                    )
                ], alignment=ft.MainAxisAlignment.END, spacing=10)
            ]
        )
        
        self.app.page.open(smtp_dialog)
    
    @property
    def google_service(self):
        """Get Google Calendar service from background service manager"""
        service = self._get_google_service()
        if service is None:
            # Return a dummy service that reports as disabled
            class DummyService:
                def is_enabled(self):
                    return False
                def sync_deadline_to_google(self, deadline):
                    return None
                def delete_deadline_from_google(self, event_id):
                    return False
            return DummyService()
        return service

    @property
    def google_calendar_settings(self):
        """Get Google Calendar settings from the settings controller (preferred) or settings view"""
        # Try settings controller first (more reliable)
        if hasattr(self.app, 'settings_controller'):
            return self.app.settings_controller.get_setting('google_services')

        # Fallback to settings view
        if hasattr(self.app, 'settings_view') and hasattr(self.app.settings_view, 'google_services_settings'):
            return self.app.settings_view.google_services_settings

        # Default fallback
        return {
            'calendar_enabled': False,
            'calendar_authenticated': False,
            'calendar_auto_sync': True,
            'calendar_delete_completed': True  # New setting for deleting completed deadlines
        }

    def _is_google_calendar_enabled(self) -> bool:
        """Check if Google Calendar sync is enabled and authenticated"""
        try:
            # First check if the Google Calendar service itself is enabled
            service_enabled = self.google_service.is_enabled()

            # Get settings from the settings controller (more reliable than settings view)
            settings = self.google_calendar_settings
            settings_enabled = settings.get('calendar_enabled', False)
            settings_authenticated = settings.get('calendar_authenticated', False)

            # Debug logging
            logger.debug(f"Google Calendar sync check - Service enabled: {service_enabled}, "
                        f"Settings enabled: {settings_enabled}, Settings authenticated: {settings_authenticated}")

            # If settings controller is available, use it for more accurate status
            if hasattr(self.app, 'settings_controller'):
                controller_settings = self.app.settings_controller.get_setting('google_services')
                settings_enabled = controller_settings.get('calendar_enabled', False)
                settings_authenticated = controller_settings.get('calendar_authenticated', False)
                logger.debug(f"Using settings controller - Enabled: {settings_enabled}, Authenticated: {settings_authenticated}")

            # If settings view is not available or not properly initialized,
            # fall back to checking the service directly
            if not hasattr(self.app, 'settings_view') and not hasattr(self.app, 'settings_controller'):
                logger.debug("Settings not available, checking service directly")
                return service_enabled

            # Use both service and settings checks
            result = (settings_enabled and settings_authenticated and service_enabled)

            # If settings say not enabled but service is enabled, refresh settings
            if not result and service_enabled:
                logger.info("Settings out of sync with service, refreshing...")
                self._refresh_google_calendar_status()
                # Re-check after refresh
                if hasattr(self.app, 'settings_controller'):
                    settings = self.app.settings_controller.get_setting('google_services')
                else:
                    settings = self.google_calendar_settings
                result = (settings.get('calendar_enabled', False) and
                         settings.get('calendar_authenticated', False) and
                         service_enabled)

            return result

        except Exception as e:
            logger.error(f"Error checking Google Calendar sync status: {e}")
            # Fallback to service check only
            return self.google_service.is_enabled()

    def _should_auto_sync(self) -> bool:
        """Check if auto-sync is enabled"""
        settings = self.google_calendar_settings
        return (self._is_google_calendar_enabled() and
                settings.get('calendar_auto_sync', True))

    def _should_delete_completed(self) -> bool:
        """Check if completed deadlines should be deleted from calendar"""
        settings = self.google_calendar_settings
        return settings.get('calendar_delete_completed', True)

    def _refresh_google_calendar_status(self):
        """Refresh Google Calendar authentication status in settings"""
        try:
            # Ensure settings view is initialized
            if not hasattr(self.app, 'settings_view'):
                logger.debug("Settings view not initialized, creating it...")
                from ui.views.settings import SettingsView
                self.app.settings_view = SettingsView(self.app)

            if hasattr(self.app.settings_view, 'google_services_settings'):
                # Update the authentication status based on current service state
                current_enabled = self.google_service.is_enabled()
                # For Google Calendar, we'll assume authenticated if enabled (like the original logic)
                current_authenticated = current_enabled

                self.app.settings_view.google_services_settings['calendar_enabled'] = current_enabled
                self.app.settings_view.google_services_settings['calendar_authenticated'] = current_authenticated

                logger.info(f"Refreshed Google Calendar status - Enabled: {current_enabled}, Authenticated: {current_authenticated}")
            else:
                logger.warning("Settings view exists but google_services_settings not found")

        except Exception as e:
            logger.error(f"Error refreshing Google Calendar status: {e}")

    def _sync_google_calendar(self, e):
        """Sync deadlines with Google Calendar"""
        # Force refresh the Google Calendar status before checking
        self._refresh_google_calendar_status()

        if not self._is_google_calendar_enabled():
            # Get detailed status for better error message
            service_enabled = self.google_service.is_enabled()
            settings = self.google_calendar_settings

            error_details = (
                f"Stato servizio Google Calendar:\n"
                f"• Servizio abilitato: {service_enabled}\n"
                f"• Impostazioni abilitate: {settings.get('calendar_enabled', False)}\n"
                f"• Impostazioni autenticate: {settings.get('calendar_authenticated', False)}"
            )

            self._show_info_dialog(
                "Google Calendar Non Configurato",
                f"Google Calendar non è configurato o autenticato.\n\n{error_details}\n\n"
                "Vai nelle Impostazioni > Servizi Google per configurare l'integrazione."
            )
            logger.info(f"Google Calendar not configured - {error_details}")
            return

        # Show loading dialog
        self._show_loading_dialog("Sincronizzazione con Google Calendar in corso...")

        try:
            # Sync all deadlines that have sync_to_google = True
            deadlines_to_sync = [d for d in self.current_deadlines if getattr(d, 'sync_to_google', True)]

            results = self.google_service.sync_all_deadlines(deadlines_to_sync)

            # Close loading dialog
            self._close_loading_dialog()

            if results["success"]:
                message = f"Sincronizzate {results['synced']}/{results['total']} scadenze"
                if results["errors"]:
                    message += f"\n{len(results['errors'])} errori"

                self._show_success_dialog("Sincronizzazione completata", message)
                self.refresh_data()  # Ricarica per vedere i cambiamenti
            else:
                self._show_error_dialog("Errore sincronizzazione", results.get("error", "Errore sconosciuto"))

        except Exception as e:
            self._close_loading_dialog()
            logger.error(f"Error during Google Calendar sync: {e}")
            self._show_error_dialog("Errore", f"Errore durante la sincronizzazione: {str(e)}")

    def _show_loading_dialog(self, message: str):
        """Show loading dialog"""
        self.loading_dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text("Sincronizzazione"),
            content=ft.Row([
                ft.ProgressRing(width=16, height=16, stroke_width=2),
                ft.Text(message)
            ], spacing=10)
        )
        self.app.page.open(self.loading_dialog)

    def _close_loading_dialog(self):
        """Close loading dialog"""
        if hasattr(self, 'loading_dialog'):
            self.app.page.close(self.loading_dialog)

    def _show_info_dialog(self, title: str, message: str):
        """Show info dialog"""
        dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text(title),
            content=ft.Text(message),
            actions=[
                ft.TextButton("OK", on_click=lambda _: self.app.page.close(dialog))
            ]
        )
        self.app.page.open(dialog)