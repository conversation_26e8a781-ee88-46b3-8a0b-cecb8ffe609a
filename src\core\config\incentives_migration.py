#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Incentives Configuration Migration Manager
Handles migration from JSON-based config to unified settings system
"""

import json
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime

from core.utils.logger import get_logger

logger = get_logger(__name__)

class IncentivesMigrationManager:
    """Manages migration of incentives configuration"""
    
    def __init__(self, app_instance):
        self.app = app_instance
        self.json_config_path = Path("data/incentives_config.json")
        self.backup_config_path = Path("data/incentives_config_backup.json")
        
    def needs_migration(self) -> bool:
        """Check if migration from JSON to main settings is needed"""
        try:
            # Check if JSON file exists and main settings don't have incentives section
            if not self.json_config_path.exists():
                return False
                
            if not hasattr(self.app, 'settings_manager') or not self.app.settings_manager:
                return False
                
            main_settings = self.app.settings_manager.settings
            incentives_in_main = main_settings.get('incentives', {})
            
            # If main settings are empty but JSON exists, migration is needed
            if not incentives_in_main and self.json_config_path.exists():
                logger.info("Migration needed: JSON config exists but main settings are empty")
                return True
                
            return False
            
        except Exception as e:
            logger.error(f"Error checking migration status: {e}")
            return False
    
    def migrate_from_json(self) -> bool:
        """Migrate configuration from JSON file to main settings"""
        try:
            if not self.json_config_path.exists():
                logger.info("No JSON config file found for migration")
                return True
                
            # Load JSON configuration
            with open(self.json_config_path, 'r', encoding='utf-8') as f:
                json_config = json.load(f)
                
            logger.info(f"Loaded JSON config with {len(json_config)} keys for migration")
            
            # Validate and clean configuration
            cleaned_config = self._clean_config(json_config)
            
            # Save to main settings
            if hasattr(self.app, 'settings_manager') and self.app.settings_manager:
                self.app.settings_manager.settings['incentives'] = cleaned_config
                self.app.settings_manager._save_settings()
                logger.info("Successfully migrated config to main settings")
                
                # Create backup of original JSON
                self._create_backup()
                
                # Optionally remove original JSON file after successful migration
                # self.json_config_path.unlink()  # Uncomment to remove after migration
                
                return True
            else:
                logger.error("Settings manager not available for migration")
                return False
                
        except Exception as e:
            logger.error(f"Error migrating from JSON: {e}")
            return False
    
    def _clean_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Clean and validate configuration data"""
        try:
            cleaned = {}
            
            # Basic settings
            cleaned['enabled'] = config.get('enabled', False)
            cleaned['frequency'] = config.get('frequency', 'weekly')
            cleaned['keywords'] = config.get('keywords', [])
            
            # LLM settings
            cleaned['llm_enabled'] = config.get('llm_enabled', False)
            cleaned['llm_model'] = config.get('llm_model', 'deepseek/deepseek-r1-0528-qwen3-8b:free')
            cleaned['llm_api_url'] = config.get('llm_api_url', 'https://openrouter.ai/api/v1')
            cleaned['openrouter_api_key'] = config.get('openrouter_api_key', '')
            
            # Email settings
            cleaned['email_notifications'] = config.get('email_notifications', True)
            cleaned['notification_email'] = config.get('notification_email', '')
            
            # Advanced settings
            cleaned['scraping_method'] = config.get('scraping_method', 'hybrid')
            cleaned['validation'] = config.get('validation', {
                'min_title_length': 5,
                'max_title_length': 300,
                'min_description_length': 5,
                'max_description_length': 2000,
                'min_relevance_score': 0.1,
                'max_duplicate_similarity': 0.95
            })
            cleaned['retry'] = config.get('retry', {
                'max_retries': 3,
                'base_delay': 1,
                'max_delay': 60,
                'exponential_base': 2,
                'jitter': True
            })
            
            # Websites configuration
            websites = config.get('websites', [])
            cleaned_websites = []
            for website in websites:
                if isinstance(website, dict) and 'name' in website and 'url' in website:
                    cleaned_websites.append({
                        'name': website.get('name', ''),
                        'url': website.get('url', ''),
                        'enabled': website.get('enabled', True),
                        'search_paths': website.get('search_paths', [])
                    })
            cleaned['websites'] = cleaned_websites
            
            # Custom models
            cleaned['custom_models'] = config.get('custom_models', {})
            
            logger.info(f"Cleaned config: {len(cleaned_websites)} websites, LLM enabled: {cleaned['llm_enabled']}")
            return cleaned
            
        except Exception as e:
            logger.error(f"Error cleaning config: {e}")
            return config  # Return original if cleaning fails
    
    def _create_backup(self):
        """Create backup of original JSON configuration"""
        try:
            if self.json_config_path.exists():
                # Add timestamp to backup
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = Path(f"data/incentives_config_backup_{timestamp}.json")
                
                # Copy original to backup
                import shutil
                shutil.copy2(self.json_config_path, backup_path)
                
                logger.info(f"Created backup of original config: {backup_path}")
                
        except Exception as e:
            logger.error(f"Error creating backup: {e}")
    
    def get_migration_status(self) -> Dict[str, Any]:
        """Get current migration status"""
        return {
            'json_exists': self.json_config_path.exists(),
            'main_settings_exist': (
                hasattr(self.app, 'settings_manager') and 
                self.app.settings_manager and 
                'incentives' in self.app.settings_manager.settings
            ),
            'needs_migration': self.needs_migration(),
            'json_config_path': str(self.json_config_path),
            'backup_config_path': str(self.backup_config_path)
        }
    
    def auto_migrate_if_needed(self) -> bool:
        """Automatically migrate if needed"""
        try:
            if self.needs_migration():
                logger.info("Auto-migration triggered")
                return self.migrate_from_json()
            else:
                logger.info("No migration needed")
                return True
                
        except Exception as e:
            logger.error(f"Error in auto-migration: {e}")
            return False 