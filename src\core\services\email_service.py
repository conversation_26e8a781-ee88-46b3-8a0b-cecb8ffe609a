#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Servizio email SMTP per Agevolami PM
"""

import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from pathlib import Path
from typing import List, Optional, Dict, Any
from datetime import datetime

from ..config import AppConfig
from ..models import Alert, Deadline, Client, Project
from ..utils import get_logger

logger = get_logger(__name__)

class EmailService:
    """Servizio per l'invio di email tramite SMTP"""
    
    def __init__(self, config: AppConfig):
        self.config = config
        self.smtp_config = config.email_config
    
    def _create_smtp_connection(self) -> smtplib.SMTP:
        """Crea una connessione SMTP"""
        try:
            if self.smtp_config["smtp_use_tls"]:
                server = smtplib.SMTP(self.smtp_config["smtp_server"], self.smtp_config["smtp_port"])
                server.starttls()
            else:
                server = smtplib.SMTP_SSL(self.smtp_config["smtp_server"], self.smtp_config["smtp_port"])
            
            if self.smtp_config["smtp_username"] and self.smtp_config["smtp_password"]:
                server.login(self.smtp_config["smtp_username"], self.smtp_config["smtp_password"])
            
            logger.info("Connessione SMTP stabilita")
            return server
            
        except Exception as e:
            logger.error(f"Errore connessione SMTP: {e}")
            raise
    
    def _create_email_message(
        self,
        to_email: str,
        subject: str,
        body: str,
        html_body: Optional[str] = None,
        attachments: Optional[List[Path]] = None
    ) -> MIMEMultipart:
        """Crea un messaggio email"""
        
        msg = MIMEMultipart('alternative')
        msg['From'] = self.smtp_config["from_email"]
        msg['To'] = to_email
        msg['Subject'] = subject
        msg['Date'] = datetime.now().strftime('%a, %d %b %Y %H:%M:%S %z')
        
        # Aggiungi corpo del messaggio
        text_part = MIMEText(body, 'plain', 'utf-8')
        msg.attach(text_part)
        
        if html_body:
            html_part = MIMEText(html_body, 'html', 'utf-8')
            msg.attach(html_part)
        
        # Aggiungi allegati
        if attachments:
            for attachment_path in attachments:
                if attachment_path.exists():
                    with open(attachment_path, 'rb') as attachment:
                        part = MIMEBase('application', 'octet-stream')
                        part.set_payload(attachment.read())
                    
                    encoders.encode_base64(part)
                    part.add_header(
                        'Content-Disposition',
                        f'attachment; filename= {attachment_path.name}'
                    )
                    msg.attach(part)
        
        return msg
    
    def send_email(
        self,
        to_email: str,
        subject: str,
        body: str,
        html_body: Optional[str] = None,
        attachments: Optional[List[Path]] = None
    ) -> bool:
        """Invia una email"""
        
        if not self.smtp_config.get("enabled", True):
            logger.warning("SMTP non abilitato, email non inviata")
            return False
        
        try:
            server = self._create_smtp_connection()
            
            msg = self._create_email_message(
                to_email, subject, body, html_body, attachments
            )
            
            server.send_message(msg)
            server.quit()
            
            logger.info(f"Email inviata a: {to_email}")
            return True
            
        except Exception as e:
            logger.error(f"Errore invio email: {e}")
            return False
    
    def send_deadline_alert(
        self,
        deadline: Deadline,
        client: Optional[Client] = None,
        project: Optional[Project] = None
    ) -> bool:
        """Invia un alert per scadenza"""
        
        if not client or not client.email:
            logger.warning(f"Cliente senza email per scadenza: {deadline.title}")
            return False
        
        # Calcola giorni rimanenti
        days_remaining = (deadline.due_date - datetime.now().date()).days
        
        # Crea oggetto email
        if days_remaining < 0:
            subject = f"🚨 SCADENZA SUPERATA: {deadline.title}"
            urgency = "SCADUTA"
        elif days_remaining == 0:
            subject = f"⚠️ SCADENZA OGGI: {deadline.title}"
            urgency = "OGGI"
        elif days_remaining <= 3:
            subject = f"🔴 SCADENZA URGENTE: {deadline.title} (tra {days_remaining} giorni)"
            urgency = "URGENTE"
        elif days_remaining <= 7:
            subject = f"🟡 SCADENZA IMMINENTE: {deadline.title} (tra {days_remaining} giorni)"
            urgency = "IMMINENTE"
        else:
            subject = f"📅 PROMEMORIA SCADENZA: {deadline.title} (tra {days_remaining} giorni)"
            urgency = "PROGRAMMATA"
        
        # Crea corpo email
        body = self._create_deadline_alert_body(
            deadline, client, project, days_remaining, urgency
        )
        
        html_body = self._create_deadline_alert_html(
            deadline, client, project, days_remaining, urgency
        )
        
        return self.send_email(
            to_email=client.email,
            subject=subject,
            body=body,
            html_body=html_body
        )
    
    def _create_deadline_alert_body(
        self,
        deadline: Deadline,
        client: Optional[Client],
        project: Optional[Project],
        days_remaining: int,
        urgency: str
    ) -> str:
        """Crea il corpo testuale dell'alert"""
        
        body = f"""
Caro/a {client.name if client else 'Cliente'},

Ti informiamo che è presente una scadenza {urgency.lower()}:

📋 DETTAGLI SCADENZA:
• Titolo: {deadline.title}
• Data scadenza: {deadline.due_date.strftime('%d/%m/%Y')}
• Giorni rimanenti: {days_remaining if days_remaining >= 0 else 'SCADUTA'}
• Priorità: {deadline.priority.value.upper()}
"""
        
        if deadline.description:
            body += f"\n• Descrizione: {deadline.description}"
        
        if project:
            body += f"""

🏢 PROGETTO ASSOCIATO:
• Nome: {project.name}
• Tipo: {project.project_type.value.replace('_', ' ').title()}
• Stato: {project.status.value.title()}
"""
        
        if deadline.notes:
            body += f"\n\n📝 NOTE:\n{deadline.notes}"
        
        body += f"""

⚠️ AZIONE RICHIESTA:
Ti preghiamo di prendere visione di questa scadenza e di completare le attività necessarie entro la data indicata.

Per qualsiasi chiarimento, non esitare a contattarci.

Cordiali saluti,
Team Agevolami.it

---
Questo è un messaggio automatico generato dal sistema di gestione scadenze Agevolami PM.
Data invio: {datetime.now().strftime('%d/%m/%Y alle %H:%M')}
"""
        
        return body
    
    def _create_deadline_alert_html(
        self,
        deadline: Deadline,
        client: Optional[Client],
        project: Optional[Project],
        days_remaining: int,
        urgency: str
    ) -> str:
        """Crea il corpo HTML dell'alert"""
        
        # Colori basati sull'urgenza
        color_map = {
            "SCADUTA": "#dc3545",
            "OGGI": "#fd7e14", 
            "URGENTE": "#dc3545",
            "IMMINENTE": "#ffc107",
            "PROGRAMMATA": "#28a745"
        }
        
        urgency_color = color_map.get(urgency, "#6c757d")
        
        html = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alert Scadenza - Agevolami PM</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px 10px 0 0; text-align: center;">
        <h1 style="margin: 0; font-size: 24px;">🔔 Alert Scadenza</h1>
        <p style="margin: 5px 0 0 0; opacity: 0.9;">Agevolami PM - Sistema di Gestione Scadenze</p>
    </div>
    
    <div style="background: #f8f9fa; padding: 20px; border: 1px solid #dee2e6;">
        <p>Caro/a <strong>{client.name if client else 'Cliente'}</strong>,</p>
        
        <p>Ti informiamo che è presente una scadenza <span style="color: {urgency_color}; font-weight: bold;">{urgency.lower()}</span>:</p>
        
        <div style="background: white; border-left: 4px solid {urgency_color}; padding: 15px; margin: 20px 0; border-radius: 0 5px 5px 0;">
            <h3 style="margin: 0 0 10px 0; color: {urgency_color};">📋 Dettagli Scadenza</h3>
            <table style="width: 100%; border-collapse: collapse;">
                <tr>
                    <td style="padding: 5px 0; font-weight: bold; width: 30%;">Titolo:</td>
                    <td style="padding: 5px 0;">{deadline.title}</td>
                </tr>
                <tr>
                    <td style="padding: 5px 0; font-weight: bold;">Data scadenza:</td>
                    <td style="padding: 5px 0; color: {urgency_color}; font-weight: bold;">{deadline.due_date.strftime('%d/%m/%Y')}</td>
                </tr>
                <tr>
                    <td style="padding: 5px 0; font-weight: bold;">Giorni rimanenti:</td>
                    <td style="padding: 5px 0; color: {urgency_color}; font-weight: bold;">{days_remaining if days_remaining >= 0 else 'SCADUTA'}</td>
                </tr>
                <tr>
                    <td style="padding: 5px 0; font-weight: bold;">Priorità:</td>
                    <td style="padding: 5px 0;">{deadline.priority.value.upper()}</td>
                </tr>
"""
        
        if deadline.description:
            html += f"""
                <tr>
                    <td style="padding: 5px 0; font-weight: bold; vertical-align: top;">Descrizione:</td>
                    <td style="padding: 5px 0;">{deadline.description}</td>
                </tr>
"""
        
        html += """
            </table>
        </div>
"""
        
        if project:
            html += f"""
        <div style="background: white; border-left: 4px solid #17a2b8; padding: 15px; margin: 20px 0; border-radius: 0 5px 5px 0;">
            <h3 style="margin: 0 0 10px 0; color: #17a2b8;">🏢 Progetto Associato</h3>
            <table style="width: 100%; border-collapse: collapse;">
                <tr>
                    <td style="padding: 5px 0; font-weight: bold; width: 30%;">Nome:</td>
                    <td style="padding: 5px 0;">{project.name}</td>
                </tr>
                <tr>
                    <td style="padding: 5px 0; font-weight: bold;">Tipo:</td>
                    <td style="padding: 5px 0;">{project.project_type.value.replace('_', ' ').title()}</td>
                </tr>
                <tr>
                    <td style="padding: 5px 0; font-weight: bold;">Stato:</td>
                    <td style="padding: 5px 0;">{project.status.value.title()}</td>
                </tr>
            </table>
        </div>
"""
        
        if deadline.notes:
            html += f"""
        <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px 0; border-radius: 5px;">
            <h4 style="margin: 0 0 10px 0; color: #856404;">📝 Note</h4>
            <p style="margin: 0; color: #856404;">{deadline.notes}</p>
        </div>
"""
        
        html += f"""
        
        <div style="background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; margin: 20px 0; border-radius: 5px;">
            <h4 style="margin: 0 0 10px 0; color: #0c5460;">⚠️ Azione Richiesta</h4>
            <p style="margin: 0; color: #0c5460;">Ti preghiamo di prendere visione di questa scadenza e di completare le attività necessarie entro la data indicata.</p>
        </div>
        
        <p>Per qualsiasi chiarimento, non esitare a contattarci.</p>
        
        <p><strong>Cordiali saluti,</strong><br>
        Team Agevolami.it</p>
    </div>
    
    <div style="background: #6c757d; color: white; padding: 15px; border-radius: 0 0 10px 10px; text-align: center; font-size: 12px;">
        <p style="margin: 0;">Questo è un messaggio automatico generato dal sistema di gestione scadenze Agevolami PM.</p>
        <p style="margin: 5px 0 0 0; opacity: 0.8;">Data invio: {datetime.now().strftime('%d/%m/%Y alle %H:%M')}</p>
    </div>
    
</body>
</html>
"""
        
        return html
    
    def test_connection(self) -> bool:
        """Testa la connessione SMTP"""
        if not self.smtp_config.get("enabled", True):
            logger.warning("SMTP non abilitato")
            return False
        
        try:
            server = self._create_smtp_connection()
            server.quit()
            logger.info("Test connessione SMTP riuscito")
            return True
        except Exception as e:
            logger.error(f"Test connessione SMTP fallito: {e}")
            return False
    
    def send_test_email(self, to_email: str) -> bool:
        """Invia una email di test"""
        subject = "Test Email - Agevolami PM"
        body = f"""
Questo è un messaggio di test dal sistema Agevolami PM.

Se ricevi questa email, la configurazione SMTP è corretta.

Data test: {datetime.now().strftime('%d/%m/%Y alle %H:%M')}

Cordiali saluti,
Sistema Agevolami PM
"""
        
        html_body = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Test Email - Agevolami PM</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: #28a745; color: white; padding: 20px; border-radius: 10px; text-align: center;">
        <h1 style="margin: 0;">✅ Test Email Riuscito</h1>
        <p style="margin: 10px 0 0 0;">Agevolami PM - Sistema di Gestione Scadenze</p>
    </div>
    
    <div style="padding: 20px; background: #f8f9fa; margin-top: 20px; border-radius: 10px;">
        <p>Questo è un messaggio di test dal sistema <strong>Agevolami PM</strong>.</p>
        <p>Se ricevi questa email, la configurazione SMTP è corretta e funzionante.</p>
        <p><strong>Data test:</strong> {datetime.now().strftime('%d/%m/%Y alle %H:%M')}</p>
        <p>Cordiali saluti,<br><strong>Sistema Agevolami PM</strong></p>
    </div>
</body>
</html>
"""
        
        return self.send_email(
            to_email=to_email,
            subject=subject,
            body=body,
            html_body=html_body
        )
    
    def send_email_with_attachment(
        self,
        to_email: str,
        subject: str,
        body: str,
        attachment_path: str
    ) -> bool:
        """Invia una email con allegato singolo"""
        
        try:
            attachment_file = Path(attachment_path)
            if not attachment_file.exists():
                logger.error(f"File allegato non trovato: {attachment_path}")
                return False
            
            return self.send_email(
                to_email=to_email,
                subject=subject,
                body=body,
                attachments=[attachment_file]
            )
            
        except Exception as e:
            logger.error(f"Errore invio email con allegato: {e}")
            return False