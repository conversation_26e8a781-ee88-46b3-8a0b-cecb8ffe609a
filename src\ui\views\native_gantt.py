#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Native Flet Gantt Chart - Fast & Responsive
Pure Flet implementation using Canvas and native components
"""

import flet as ft
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from dataclasses import dataclass
from core import get_logger
from core.models import Project, ProjectStatus, Deadline, DeadlineStatus, Task, TaskStatus

logger = get_logger(__name__)

@dataclass
class GanttTask:
    """Gantt chart task item"""
    id: str
    name: str
    start_date: datetime
    end_date: datetime
    progress: float
    status: str
    color: str
    is_milestone: bool = False

class NativeGanttChart:
    """
    🚀 Ultra-fast native Flet Gantt chart
    No external dependencies, pure Flet performance!
    """
    
    def __init__(self, app_instance):
        self.app = app_instance
        self.db = app_instance.db_manager
        
        # Chart dimensions
        self.chart_width = 1000
        self.row_height = 45
        self.label_width = 250
        
        # Data
        self.tasks: List[GanttTask] = []
        self.filtered_tasks: List[GanttTask] = []
        
        # Date range
        self.start_date = datetime.now() - timedelta(days=30)
        self.end_date = datetime.now() + timedelta(days=90)
        self.total_days = (self.end_date - self.start_date).days
        
        # Filters
        self.filter_client = "All"
        self.filter_project = "All"  # New project filter
        self.filter_status = "All"
        self.show_completed = False  # Hide completed tasks by default
        self.show_tasks = False  # New toggle for tasks
        self.zoom_factor = 1.0
        self.period_filter = "1m"  # "1m", "3m", "6m", "1y", "all" - Default: 1 month
        
        # Colors for project and deadline statuses
        self.status_colors = {
            # Project statuses
            "bozza": "#9E9E9E",
            "presentato": "#2196F3", 
            "approvato": "#4CAF50",
            "in_corso": "#4CAF50",
            "completato": "#9E9E9E",
            "sospeso": "#FF9800",
            "annullato": "#F44336",
            # Deadline statuses
            "in_attesa": "#2196F3",
            "completato": "#4CAF50", 
            "scaduto": "#F44336",
            "annullato": "#9E9E9E",
            # Generic
            "OVERDUE": "#E91E63"
        }
        
        self.chart_container = None
        self.stats_container = None

        # Data storage
        self.projects = []
        self.clients = []
        
    def load_data(self):
        """📊 Load and process project data"""
        try:
            self.projects = self.db.get_all_projects()
            deadlines = self.db.get_all_deadlines()
            self.clients = self.db.get_all_clients()

            self.tasks = []

            # Convert projects to tasks
            for project in self.projects:
                if project.start_date and project.end_date:
                    # Convert date to datetime
                    start_dt = datetime.combine(project.start_date, datetime.min.time())
                    end_dt = datetime.combine(project.end_date, datetime.min.time())
                    
                    self.tasks.append(GanttTask(
                        id=str(project.id),
                        name=f"📋 {project.name}",
                        start_date=start_dt,
                        end_date=end_dt,
                        progress=getattr(project, 'progress', 0),
                        status=project.status.value if hasattr(project.status, 'value') else str(project.status),
                        color=self.status_colors.get(str(project.status), "#2196F3")
                    ))
            
            # Convert deadlines to milestones
            for deadline in deadlines:
                if deadline.due_date:
                    status = deadline.status.value if hasattr(deadline.status, 'value') else str(deadline.status)
                    if deadline.due_date < datetime.now().date() and status == "in_attesa":
                        status = "OVERDUE"
                    
                    # Convert date to datetime
                    due_dt = datetime.combine(deadline.due_date, datetime.min.time())
                    
                    self.tasks.append(GanttTask(
                        id=str(deadline.id),
                        name=f"📅 {deadline.title}",
                        start_date=due_dt,
                        end_date=due_dt,
                        progress=100 if status == "COMPLETED" else 0,
                        status=status,
                        color=self.status_colors.get(status, "#FF5722"),
                        is_milestone=True
                    ))

            # Convert tasks to gantt tasks if enabled
            if self.show_tasks:
                # Get tasks in current date range
                period_start, period_end = self._get_period_range()
                if period_start and period_end:
                    start_date = period_start.date()
                    end_date = period_end.date()
                else:
                    start_date = (datetime.now() - timedelta(days=30)).date()
                    end_date = (datetime.now() + timedelta(days=90)).date()

                db_tasks = self.db.get_tasks_by_date_range(start_date, end_date)

                for task in db_tasks:
                    # Determine task dates
                    task_start = task.start_date if task.start_date else task.created_at.date()
                    task_end = task.due_date.date() if task.due_date else task_start

                    # Convert to datetime
                    start_dt = datetime.combine(task_start, datetime.min.time())
                    end_dt = datetime.combine(task_end, datetime.min.time())

                    # Get task status
                    task_status = task.status.value if hasattr(task.status, 'value') else str(task.status)

                    self.tasks.append(GanttTask(
                        id=f"task_{task.id}",
                        name=f"📋 {task.title}",
                        start_date=start_dt,
                        end_date=end_dt,
                        progress=task.progress_percentage,
                        status=task_status,
                        color=self.status_colors.get(task_status, "#9C27B0"),  # Purple for tasks
                        is_milestone=False
                    ))

            # Sort by start date
            self.tasks.sort(key=lambda x: x.start_date)
            
            # Update date range
            if self.tasks:
                all_dates = []
                for task in self.tasks:
                    all_dates.extend([task.start_date, task.end_date])
                
                self.start_date = min(all_dates) - timedelta(days=7)
                self.end_date = max(all_dates) + timedelta(days=14)
                self.total_days = (self.end_date - self.start_date).days
            
            self._apply_filters()
            logger.info(f"🎯 Loaded {len(self.tasks)} tasks for Native Gantt")
            
        except Exception as e:
            logger.error(f"❌ Error loading Gantt data: {e}")
            self.tasks = []
            self.filtered_tasks = []
    
    def _apply_filters(self):
        """🔍 Apply current filters to tasks"""
        self.filtered_tasks = []

        # Get period filter date range
        period_start, period_end = self._get_period_range()

        for task in self.tasks:
            # Apply completed filter
            if not self.show_completed and task.status in ["completato", "COMPLETED"]:
                continue

            # Apply project filter
            if self.filter_project != "All":
                # For tasks, check if they belong to the selected project
                if task.id.startswith("task_"):
                    # This is a task - need to check its project association
                    task_id = task.id.replace("task_", "")
                    try:
                        from uuid import UUID
                        db_task = self.db.get_task(UUID(task_id))
                        if db_task and db_task.project_id and str(db_task.project_id) != self.filter_project:
                            continue
                    except:
                        continue
                else:
                    # This is a project or deadline
                    if not task.id == self.filter_project:
                        # For deadlines, check project association
                        if task.is_milestone:
                            try:
                                from uuid import UUID
                                deadline = self.db.get_deadline(UUID(task.id))
                                if deadline and deadline.project_id and str(deadline.project_id) != self.filter_project:
                                    continue
                            except:
                                continue
                        else:
                            # This is a project, skip if not matching
                            continue

            # Apply period filter
            if self.period_filter != "all":
                # Check if task overlaps with period
                task_end = task.end_date if not task.is_milestone else task.start_date
                if task_end < period_start or task.start_date > period_end:
                    continue

            self.filtered_tasks.append(task)
        
        # Update date range if period filter is active
        if self.period_filter != "all" and period_start and period_end:
            self.start_date = period_start
            self.end_date = period_end
            self.total_days = (self.end_date - self.start_date).days
        elif self.tasks:
            # Reset to full range
            all_dates = []
            for task in self.tasks:
                all_dates.extend([task.start_date, task.end_date])
            
            self.start_date = min(all_dates) - timedelta(days=7)
            self.end_date = max(all_dates) + timedelta(days=14)
            self.total_days = (self.end_date - self.start_date).days
    
    def _get_period_range(self):
        """Get start and end dates for current period filter"""
        now = datetime.now()
        
        if self.period_filter == "1m":
            start = now - timedelta(days=15)
            end = now + timedelta(days=45)  # 1 month ahead
        elif self.period_filter == "3m":
            start = now - timedelta(days=30)
            end = now + timedelta(days=120)  # 3 months ahead
        elif self.period_filter == "6m":
            start = now - timedelta(days=60)
            end = now + timedelta(days=240)  # 6 months ahead
        elif self.period_filter == "1y":
            start = now - timedelta(days=90)
            end = now + timedelta(days=365)  # 1 year ahead
        else:  # "all"
            return None, None
        
        return start, end
    
    def _create_timeline_header(self) -> ft.Container:
        """📅 Create timeline header with dates"""
        if self.total_days <= 0:
            return ft.Container(height=40, bgcolor=ft.Colors.GREY_100)
        
        # Calculate week intervals - using a more reasonable approach
        num_intervals = max(4, min(12, self.total_days // 5))  # 4-12 intervals
        interval_width = self.chart_width / num_intervals
        days_per_interval = self.total_days / num_intervals
        
        # Create timeline using Row instead of Stack for better layout
        timeline_cells = []
        current_date = self.start_date
        
        for i in range(num_intervals):
            # Format date label
            date_label = current_date.strftime("%d/%m")
            if i == 0:
                date_label = current_date.strftime("%d %b")  # First cell with month
            
            timeline_cells.append(
                ft.Container(
                    content=ft.Text(
                        date_label,
                        size=10,
                        color=ft.Colors.GREY_700,
                        text_align=ft.TextAlign.CENTER,
                        weight=ft.FontWeight.W_500
                    ),
                    width=interval_width,
                    height=35,
                    alignment=ft.alignment.center,
                    border=ft.border.all(0.5, ft.Colors.GREY_300),
                    bgcolor=ft.Colors.GREY_50 if i % 2 == 0 else ft.Colors.WHITE
                )
            )
            
            # Advance date
            current_date += timedelta(days=int(days_per_interval))
        
        # Create timeline row
        timeline_row = ft.Row(
            controls=timeline_cells,
            spacing=0,
            run_spacing=0
        )
        
        # Add today marker overlay if needed
        timeline_content = [timeline_row]
        
        today_pos = self._date_to_x(datetime.now())
        if 0 <= today_pos <= self.chart_width:
            today_marker = ft.Container(
                content=ft.Stack([
                    # Red line
                    ft.Container(
                        bgcolor=ft.Colors.RED,
                        width=2,
                        height=35
                    ),
                    # Today label
                    ft.Container(
                        content=ft.Text(
                            "TODAY",
                            size=7,
                            color=ft.Colors.WHITE,
                            weight=ft.FontWeight.BOLD
                        ),
                        bgcolor=ft.Colors.RED,
                        padding=ft.padding.symmetric(horizontal=3, vertical=1),
                        border_radius=2,
                        top=-15,
                        left=-8
                    )
                ]),
                left=today_pos,
                top=0,
                width=2,
                height=35
            )
            
            # Wrap in stack for overlay
            timeline_content = [
                ft.Stack([
                    timeline_row,
                    today_marker
                ])
            ]
        
        return ft.Container(
            content=ft.Column(timeline_content, spacing=0),
            width=self.chart_width,
            height=40,
            bgcolor=ft.Colors.GREY_100,
            border=ft.border.all(1, ft.Colors.GREY_300)
        )
    
    def _date_to_x(self, date: datetime) -> float:
        """Convert date to X coordinate"""
        if self.total_days <= 0:
            return 0
        days_from_start = (date - self.start_date).days
        return (days_from_start / self.total_days) * self.chart_width
    
    def _create_task_row(self, task: GanttTask, y_pos: int) -> List[ft.Control]:
        """🎨 Create visual elements for a task row"""
        elements = []
        
        # Row background
        elements.append(
            ft.Container(
                bgcolor=ft.Colors.WHITE if y_pos % 2 == 0 else ft.Colors.GREY_50,
                left=0,
                top=y_pos,
                width=self.chart_width,
                height=self.row_height,
                border=ft.border.all(0.5, ft.Colors.GREY_200)
            )
        )
        
        if task.is_milestone:
            # 💎 Milestone (diamond)
            x_pos = self._date_to_x(task.start_date)
            icon_with_tooltip = ft.Icon(
                ft.Icons.DIAMOND,
                color=task.color,
                size=20,
                tooltip=f"{task.name}\n📅 {task.start_date.strftime('%d/%m/%Y')}\n⭐ {task.status}"
            )
            
            elements.append(
                ft.Container(
                    content=icon_with_tooltip,
                    left=x_pos - 10,
                    top=y_pos + 12,
                    width=20,
                    height=20
                )
            )
        else:
            # 📊 Project bar
            start_x = self._date_to_x(task.start_date)
            end_x = self._date_to_x(task.end_date)
            bar_width = max(20, end_x - start_x)
            
            # Progress bar
            progress_width = bar_width * (task.progress / 100) if task.progress > 0 else bar_width
            
            elements.extend([
                # Background bar
                ft.Container(
                    bgcolor=ft.Colors.GREY_300,
                    left=start_x,
                    top=y_pos + 15,
                    width=bar_width,
                    height=15,
                    border_radius=7
                ),
                # Progress bar with tooltip
                ft.Container(
                    bgcolor=task.color,
                    left=start_x,
                    top=y_pos + 15,
                    width=progress_width,
                    height=15,
                    border_radius=7,
                    opacity=0.9,
                    tooltip=f"{task.name}\n📅 {task.start_date.strftime('%d/%m/%Y')} → {task.end_date.strftime('%d/%m/%Y')}\n📊 {task.progress}% complete\n⭐ {task.status}"
                )
            ])
        
        return elements
    
    def _create_labels_column(self) -> ft.Container:
        """📝 Create task labels column"""
        if not self.filtered_tasks:
            return ft.Container(
                content=ft.Text("No tasks", color=ft.Colors.GREY_600),
                width=self.label_width,
                alignment=ft.alignment.center
            )
        
        labels = []
        for i, task in enumerate(self.filtered_tasks):
            # Status icon
            status_icon = ft.Icons.SCHEDULE if task.is_milestone else ft.Icons.WORK
            if task.status in ["completato", "COMPLETED"]:
                status_icon = ft.Icons.CHECK_CIRCLE
            elif task.status in ["sospeso", "PAUSED"]:
                status_icon = ft.Icons.PAUSE_CIRCLE
            elif task.status in ["annullato", "CANCELLED"]:
                status_icon = ft.Icons.CANCEL
            elif task.status in ["OVERDUE", "scaduto"]:
                status_icon = ft.Icons.WARNING
            
            labels.append(
                ft.Container(
                    content=ft.Row([
                        ft.Icon(status_icon, color=task.color, size=16),
                        ft.Text(
                            task.name[:30] + "..." if len(task.name) > 30 else task.name,
                            size=12,
                            color=ft.Colors.GREY_800,
                            overflow=ft.TextOverflow.ELLIPSIS,
                            expand=True
                        )
                    ], spacing=8),
                    padding=ft.padding.symmetric(horizontal=10, vertical=12),
                    bgcolor=ft.Colors.WHITE if i % 2 == 0 else ft.Colors.GREY_50,
                    border=ft.border.all(0.5, ft.Colors.GREY_200),
                    height=self.row_height
                )
            )
        
        return ft.Container(
            content=ft.Column(labels, spacing=0),
            width=self.label_width,
            bgcolor=ft.Colors.GREY_50
        )
    
    def _create_chart_area(self) -> ft.Container:
        """🎯 Create main chart area"""
        if not self.filtered_tasks:
            return ft.Container(
                content=ft.Text(
                    "📊 Nessun dato\n\nAggiungi progetti per vedere il Gantt",
                    text_align=ft.TextAlign.CENTER,
                    size=16,
                    color=ft.Colors.GREY_600
                ),
                width=self.chart_width,
                height=200,
                alignment=ft.alignment.center,
                bgcolor=ft.Colors.GREY_50
            )
        
        chart_elements = []
        
        # Create task rows
        for i, task in enumerate(self.filtered_tasks):
            y_pos = i * self.row_height
            task_elements = self._create_task_row(task, y_pos)
            chart_elements.extend(task_elements)
        
        total_height = len(self.filtered_tasks) * self.row_height
        
        return ft.Container(
            content=ft.Stack(chart_elements),
            width=self.chart_width,
            height=total_height,
            bgcolor=ft.Colors.WHITE
        )
    
    def _create_controls(self) -> ft.Container:
        """🎛️ Create control panel"""
        
        def refresh_chart(e=None):
            self.load_data()
            self._rebuild_chart()
        
        def toggle_completed(e):
            self.show_completed = e.control.value
            self._apply_filters()
            self._rebuild_chart()
        
        def zoom_chart(e):
            self.zoom_factor = float(e.control.value)
            self.chart_width = int(1000 * self.zoom_factor)
            self._rebuild_chart()
        
        def change_period(e):
            self.period_filter = e.control.value
            self._apply_filters()
            self._rebuild_chart()

        def change_project_filter(e):
            self.filter_project = e.control.value
            self._apply_filters()
            self._rebuild_chart()

        def toggle_tasks(e):
            self.show_tasks = e.control.value
            self.load_data()  # Reload data to include/exclude tasks
            self._rebuild_chart()
        
        def export_png(e):
            """Export chart as PNG image"""
            try:
                import os
                from datetime import datetime
                
                # Get better export directory (Documents folder)
                export_dir = self._get_export_directory()
                
                # Generate filename
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = os.path.join(export_dir, f"gantt_chart_{timestamp}.png")
                
                # Create image
                self._create_image_export(filename, "PNG")
                
                # Show export success dialog
                self._show_export_success_dialog(filename, "PNG")
                    
            except Exception as ex:
                logger.error(f"PNG export error: {ex}")
                self._show_export_error_dialog(f"Errore export PNG: {str(ex)}")
        
        def export_pdf(e):
            """Export chart as PDF"""
            try:
                import os
                from datetime import datetime
                
                # Get better export directory (Documents folder)
                export_dir = self._get_export_directory()
                
                # Generate filename
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = os.path.join(export_dir, f"gantt_chart_{timestamp}.pdf")
                
                # Create PDF
                self._create_image_export(filename, "PDF")
                
                # Show export success dialog
                self._show_export_success_dialog(filename, "PDF")
                    
            except Exception as ex:
                logger.error(f"PDF export error: {ex}")
                self._show_export_error_dialog(f"Errore export PDF: {str(ex)}")
        
        # Export buttons dropdown
        export_items = [
            ft.dropdown.Option(key="png", text="📊 PNG"),
            ft.dropdown.Option(key="pdf", text="📄 PDF"),
        ]
        
        # Period filter dropdown
        period_options = [
            ft.dropdown.Option(key="1m", text="📅 1 Mese"),
            ft.dropdown.Option(key="3m", text="📅 3 Mesi"),
            ft.dropdown.Option(key="6m", text="📅 6 Mesi"),
            ft.dropdown.Option(key="1y", text="📅 1 Anno"),
            ft.dropdown.Option(key="all", text="📅 Tutto"),
        ]
        
        # Project filter dropdown
        project_options = [ft.dropdown.Option(key="All", text="🏢 Tutti i progetti")]
        for project in self.projects:
            project_options.append(
                ft.dropdown.Option(
                    key=str(project.id),
                    text=f"📋 {project.name[:25]}{'...' if len(project.name) > 25 else ''}"
                )
            )

        project_dropdown = ft.Dropdown(
            label="Progetto",
            value=self.filter_project,
            options=project_options,
            width=200,
            dense=True,
            on_change=change_project_filter
        )

        period_dropdown = ft.Dropdown(
            label="Periodo",
            value=self.period_filter,
            options=period_options,
            width=120,
            dense=True,
            on_change=change_period
        )
        
        export_dropdown = ft.Dropdown(
            label="Export",
            hint_text="Formato",
            options=export_items,
            width=100,
            dense=True,
            on_change=lambda e: export_png(e) if e.control.value == "png" else export_pdf(e)
        )
        
        return ft.Container(
            content=ft.Column([
                # First row - main controls
                ft.Row([
                    ft.ElevatedButton(
                        "🔄 Aggiorna",
                        icon=ft.Icons.REFRESH,
                        on_click=refresh_chart,
                        bgcolor=ft.Colors.BLUE_600,
                        color=ft.Colors.WHITE
                    ),
                    ft.VerticalDivider(width=10),
                    project_dropdown,
                    period_dropdown,
                    ft.Checkbox(
                        label="Mostra completati",
                        value=self.show_completed,
                        on_change=toggle_completed
                    ),
                    ft.Checkbox(
                        label="📋 Mostra Tasks",
                        value=self.show_tasks,
                        on_change=toggle_tasks
                    ),
                ], spacing=15, scroll=ft.ScrollMode.AUTO),

                # Second row - zoom and export
                ft.Row([
                    ft.Text("Zoom:", size=12, color=ft.Colors.GREY_600),
                    ft.Slider(
                        value=self.zoom_factor,
                        min=0.5,
                        max=2.0,
                        divisions=6,
                        on_change=zoom_chart,
                        width=150
                    ),
                    ft.Text(f"{self.zoom_factor:.1f}x", size=12, color=ft.Colors.GREY_600),
                    ft.VerticalDivider(width=20),
                    ft.ElevatedButton(
                        "📊 PNG",
                        icon=ft.Icons.DOWNLOAD,
                        on_click=export_png,
                        bgcolor=ft.Colors.GREEN_600,
                        color=ft.Colors.WHITE
                    ),
                    ft.ElevatedButton(
                        "📄 PDF",
                        icon=ft.Icons.PICTURE_AS_PDF,
                        on_click=export_pdf,
                        bgcolor=ft.Colors.RED_600,
                        color=ft.Colors.WHITE
                    )
                ], spacing=15, scroll=ft.ScrollMode.AUTO)
            ], spacing=10),
            padding=ft.padding.all(15),
            bgcolor=ft.Colors.GREY_100,
            border_radius=8
        )
    
    def _create_stats(self) -> ft.Container:
        """📈 Create statistics panel"""
        total_tasks = len(self.tasks)
        active_tasks = len([t for t in self.tasks if t.status in ["in_corso", "in_attesa", "presentato", "approvato"]])
        completed_tasks = len([t for t in self.tasks if t.status in ["completato", "COMPLETED"]])
        overdue_tasks = len([t for t in self.tasks if t.status in ["OVERDUE", "scaduto"]])
        
        return ft.Row([
            ft.Container(
                content=ft.Column([
                    ft.Text("Total", size=10, color=ft.Colors.GREY_600),
                    ft.Text(str(total_tasks), size=18, weight=ft.FontWeight.BOLD)
                ], spacing=2, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                padding=ft.padding.all(8),
                bgcolor=ft.Colors.BLUE_50,
                border_radius=5,
                width=60
            ),
            ft.Container(
                content=ft.Column([
                    ft.Text("Active", size=10, color=ft.Colors.GREY_600),
                    ft.Text(str(active_tasks), size=18, weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN_600)
                ], spacing=2, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                padding=ft.padding.all(8),
                bgcolor=ft.Colors.GREEN_50,
                border_radius=5,
                width=60
            ),
            ft.Container(
                content=ft.Column([
                    ft.Text("Done", size=10, color=ft.Colors.GREY_600),
                    ft.Text(str(completed_tasks), size=18, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_600)
                ], spacing=2, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                padding=ft.padding.all(8),
                bgcolor=ft.Colors.GREY_50,
                border_radius=5,
                width=60
            ),
            ft.Container(
                content=ft.Column([
                    ft.Text("Overdue", size=10, color=ft.Colors.GREY_600),
                    ft.Text(str(overdue_tasks), size=18, weight=ft.FontWeight.BOLD, color=ft.Colors.RED_600)
                ], spacing=2, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                padding=ft.padding.all(8),
                bgcolor=ft.Colors.RED_50,
                border_radius=5,
                width=60
            )
        ], spacing=10)
    
    def _rebuild_chart(self):
        """🔄 Rebuild chart components"""
        if self.chart_container:
            # Update timeline
            timeline = self._create_timeline_header()
            labels = self._create_labels_column()
            chart = self._create_chart_area()
            
            # Create scrollable chart area
            scrollable_chart = ft.Container(
                content=ft.Column([timeline, chart], spacing=0),
                width=self.chart_width,
                height=min(600, len(self.filtered_tasks) * self.row_height + 60)
            )
            
            # Update main chart content with horizontal scroll
            new_content = ft.Row([
                labels,
                ft.Container(
                    content=ft.Row([scrollable_chart], scroll=ft.ScrollMode.AUTO),
                    expand=True
                )
            ], spacing=0)
            
            self.chart_container.content = new_content
            
            # Update stats
            if self.stats_container:
                self.stats_container.content = self._create_stats()
            
            if hasattr(self.app, 'page'):
                self.app.page.update()
    
    def _generate_html_export(self) -> str:
        """Generate HTML export of the Gantt chart"""
        
        # Calculate chart dimensions
        chart_width = 800
        timeline_height = 40
        total_height = len(self.filtered_tasks) * self.row_height + timeline_height
        
        # Generate timeline dates
        if not self.start_date or not self.end_date:
            return "<html><body><h1>No data to export</h1></body></html>"
        
        html = f"""
<!DOCTYPE html>
<html>
<head>
    <title>Gantt Chart Export</title>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }}
        .header {{ text-align: center; margin-bottom: 30px; background: white; padding: 20px; border-radius: 8px; }}
        .gantt-container {{ background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
        .chart-area {{ display: flex; border: 1px solid #ddd; border-radius: 5px; overflow: hidden; }}
        .labels {{ width: 250px; background: #f8f9fa; }}
        .chart {{ flex: 1; position: relative; overflow-x: auto; }}
        .label-row {{ padding: 12px 10px; border-bottom: 1px solid #eee; height: {self.row_height}px; display: flex; align-items: center; }}
        .label-row:nth-child(odd) {{ background: #fff; }}
        .label-row:nth-child(even) {{ background: #f8f9fa; }}
        .timeline {{ height: {timeline_height}px; background: #e9ecef; border-bottom: 1px solid #ddd; position: relative; }}
        .chart-content {{ height: {len(self.filtered_tasks) * self.row_height}px; position: relative; width: {chart_width}px; }}
        .task-bar {{ position: absolute; height: 15px; border-radius: 7px; opacity: 0.9; }}
        .milestone {{ position: absolute; width: 20px; height: 20px; transform: rotate(45deg); }}
        .timeline-label {{ position: absolute; font-size: 11px; color: #6c757d; top: 5px; }}
        .today-line {{ position: absolute; width: 2px; background: red; height: 100%; }}
        .stats {{ display: flex; gap: 10px; margin-bottom: 20px; }}
        .stat-box {{ background: white; padding: 10px; border-radius: 5px; text-align: center; min-width: 60px; }}
        .status-icon {{ display: inline-block; width: 16px; height: 16px; margin-right: 8px; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Gantt Chart Export</h1>
        <p>Exported on {datetime.now().strftime('%d/%m/%Y %H:%M')}</p>
    </div>
    
    <div class="stats">
        <div class="stat-box" style="background: #e3f2fd;">
            <div>Total</div>
            <div style="font-size: 18px; font-weight: bold;">{len(self.tasks)}</div>
        </div>
        <div class="stat-box" style="background: #e8f5e8;">
            <div>Active</div>
            <div style="font-size: 18px; font-weight: bold; color: #28a745;">{len([t for t in self.tasks if t.status in ["in_corso", "in_attesa", "presentato", "approvato"]])}</div>
        </div>
        <div class="stat-box" style="background: #f8f9fa;">
            <div>Done</div>
            <div style="font-size: 18px; font-weight: bold; color: #6c757d;">{len([t for t in self.tasks if t.status in ["completato", "COMPLETED"]])}</div>
        </div>
        <div class="stat-box" style="background: #ffeeba;">
            <div>Overdue</div>
            <div style="font-size: 18px; font-weight: bold; color: #dc3545;">{len([t for t in self.tasks if t.status in ["OVERDUE", "scaduto"]])}</div>
        </div>
    </div>
    
    <div class="gantt-container">
        <div class="chart-area">
            <div class="labels">
"""
        
        # Add task labels
        for i, task in enumerate(self.filtered_tasks):
            icon = "💎" if task.is_milestone else "📋"
            html += f'                <div class="label-row">{icon} {task.name[:40]}{"..." if len(task.name) > 40 else ""}</div>\\n'
        
        html += f"""
            </div>
            <div class="chart">
                <div class="timeline">
"""
        
        # Add timeline labels
        num_intervals = max(4, min(12, self.total_days // 5))
        interval_width = chart_width / num_intervals
        days_per_interval = self.total_days / num_intervals
        current_date = self.start_date
        
        for i in range(num_intervals):
            x_pos = i * interval_width
            date_text = current_date.strftime("%d/%m") if i > 0 else current_date.strftime("%d %b")
            html += f'                    <div class="timeline-label" style="left: {x_pos}px;">{date_text}</div>\\n'
            current_date += timedelta(days=int(days_per_interval))
        
        # Today line
        today_pos = self._date_to_x(datetime.now())
        if 0 <= today_pos <= chart_width:
            html += f'                    <div class="today-line" style="left: {today_pos}px;"></div>\\n'
        
        html += """
                </div>
                <div class="chart-content">
"""
        
        # Add task bars
        for i, task in enumerate(self.filtered_tasks):
            y_pos = i * self.row_height
            
            if task.is_milestone:
                x_pos = self._date_to_x(task.start_date)
                html += f'                    <div class="milestone" style="left: {x_pos-10}px; top: {y_pos+12}px; background: {task.color};" title="{task.name}"></div>\\n'
            else:
                start_x = self._date_to_x(task.start_date)
                end_x = self._date_to_x(task.end_date)
                bar_width = max(20, end_x - start_x)
                
                html += f'                    <div class="task-bar" style="left: {start_x}px; top: {y_pos+18}px; width: {bar_width}px; background: {task.color};" title="{task.name} - {task.start_date.strftime('%d/%m/%Y')} to {task.end_date.strftime('%d/%m/%Y')}"></div>\\n'
        
        html += """
                </div>
            </div>
        </div>
    </div>
    
    <div style="text-align: center; margin-top: 20px; color: #6c757d;">
        <small>Generated by Agevolami PM - Native Gantt Chart</small>
    </div>
</body>
</html>
"""
        return html
    
    def _create_image_export(self, filename: str, format_type: str):
        """Create PNG/PDF export using PIL"""
        try:
            from PIL import Image, ImageDraw, ImageFont
            import os
            
            # Image dimensions
            img_width = max(1200, self.chart_width + self.label_width + 100)
            img_height = max(800, len(self.filtered_tasks) * self.row_height + 200)
            
            # Create image
            image = Image.new('RGB', (img_width, img_height), 'white')
            draw = ImageDraw.Draw(image)
            
            try:
                # Try to load a font
                font_title = ImageFont.truetype("arial.ttf", 24)
                font_label = ImageFont.truetype("arial.ttf", 12)
                font_small = ImageFont.truetype("arial.ttf", 10)
            except:
                # Fallback to default font
                font_title = ImageFont.load_default()
                font_label = ImageFont.load_default()
                font_small = ImageFont.load_default()
            
            # Draw header
            draw.text((20, 20), "🚀 Gantt Chart Export", fill='black', font=font_title)
            draw.text((20, 50), f"Generated: {datetime.now().strftime('%d/%m/%Y %H:%M')}", fill='gray', font=font_small)
            draw.text((20, 70), f"Period: {self.period_filter.upper()}", fill='gray', font=font_small)
            
            # Draw stats
            stats_y = 100
            total_tasks = len(self.tasks)
            active_tasks = len([t for t in self.tasks if t.status in ["in_corso", "in_attesa", "presentato", "approvato"]])
            completed_tasks = len([t for t in self.tasks if t.status in ["completato", "COMPLETED"]])
            
            draw.text((20, stats_y), f"📊 Total: {total_tasks} | Active: {active_tasks} | Done: {completed_tasks}", fill='blue', font=font_label)
            
            # Chart start position
            chart_start_y = 140
            label_width = 250
            
            # Draw timeline header
            timeline_y = chart_start_y
            if self.total_days > 0:
                num_intervals = max(4, min(12, self.total_days // 5))
                interval_width = (img_width - label_width - 40) / num_intervals
                days_per_interval = self.total_days / num_intervals
                current_date = self.start_date
                
                for i in range(num_intervals):
                    x_pos = label_width + 20 + (i * interval_width)
                    date_text = current_date.strftime("%d/%m") if i > 0 else current_date.strftime("%d %b")
                    
                    # Draw timeline cell
                    cell_color = '#f0f0f0' if i % 2 == 0 else 'white'
                    draw.rectangle([x_pos, timeline_y, x_pos + interval_width, timeline_y + 30], fill=cell_color, outline='gray')
                    draw.text((x_pos + 5, timeline_y + 8), date_text, fill='black', font=font_small)
                    
                    current_date += timedelta(days=int(days_per_interval))
            
            # Draw task rows
            task_start_y = chart_start_y + 35
            for i, task in enumerate(self.filtered_tasks):
                y_pos = task_start_y + (i * self.row_height)
                
                # Draw label
                status_icon = "💎" if task.is_milestone else "📋"
                label_text = f"{status_icon} {task.name[:35]}{'...' if len(task.name) > 35 else ''}"
                
                # Label background
                label_bg = 'white' if i % 2 == 0 else '#f8f8f8'
                draw.rectangle([0, y_pos, label_width, y_pos + self.row_height], fill=label_bg, outline='lightgray')
                draw.text((10, y_pos + 15), label_text, fill='black', font=font_label)
                
                # Draw task bar
                if self.total_days > 0:
                    chart_area_width = img_width - label_width - 40
                    start_x = label_width + 20 + ((task.start_date - self.start_date).days / self.total_days) * chart_area_width
                    
                    if task.is_milestone:
                        # Draw milestone diamond
                        diamond_size = 8
                        diamond_points = [
                            (start_x, y_pos + 20),
                            (start_x + diamond_size, y_pos + 20 + diamond_size),
                            (start_x, y_pos + 20 + 2*diamond_size),
                            (start_x - diamond_size, y_pos + 20 + diamond_size)
                        ]
                        draw.polygon(diamond_points, fill=task.color)
                    else:
                        # Draw project bar
                        end_x = label_width + 20 + ((task.end_date - self.start_date).days / self.total_days) * chart_area_width
                        bar_width = max(20, end_x - start_x)
                        
                        # Background bar
                        draw.rectangle([start_x, y_pos + 18, start_x + bar_width, y_pos + 33], fill='lightgray', outline='gray')
                        
                        # Progress bar
                        progress_width = bar_width * (task.progress / 100) if task.progress > 0 else bar_width
                        draw.rectangle([start_x, y_pos + 18, start_x + progress_width, y_pos + 33], fill=task.color)
                        
                        # Progress text
                        if bar_width > 30:
                            progress_text = f"{task.progress}%"
                            draw.text((start_x + 5, y_pos + 20), progress_text, fill='white', font=font_small)
            
            # Today line
            if self.total_days > 0:
                today_pos = label_width + 20 + ((datetime.now() - self.start_date).days / self.total_days) * (img_width - label_width - 40)
                if label_width + 20 <= today_pos <= img_width - 20:
                    draw.line([today_pos, chart_start_y, today_pos, chart_start_y + len(self.filtered_tasks) * self.row_height + 30], fill='red', width=2)
                    draw.text((today_pos - 15, chart_start_y - 15), "TODAY", fill='red', font=font_small)
            
            # Save image
            if format_type == "PDF":
                image.save(filename, "PDF", resolution=100.0)
            else:
                image.save(filename, format_type)
                
            logger.info(f"✅ {format_type} export created: {filename}")
            
        except Exception as e:
            logger.error(f"❌ Error creating {format_type} export: {e}")
            raise e
    
    def export_png(self):
        """Export chart as PNG - keyboard shortcut method"""
        try:
            import os
            from datetime import datetime
            
            # Get better export directory (Documents folder)
            export_dir = self._get_export_directory()
            
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = os.path.join(export_dir, f"gantt_chart_{timestamp}.png")
            
            # Create image
            self._create_image_export(filename, "PNG")
            
            # Show export success dialog
            self._show_export_success_dialog(filename, "PNG")
                
        except Exception as ex:
            logger.error(f"PNG export error: {ex}")
            self._show_export_error_dialog(f"Errore export PNG: {str(ex)}")
    
    def export_pdf(self):
        """Export chart as PDF - keyboard shortcut method"""
        try:
            import os
            from datetime import datetime
            
            # Get better export directory (Documents folder)
            export_dir = self._get_export_directory()
            
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = os.path.join(export_dir, f"gantt_chart_{timestamp}.pdf")
            
            # Create PDF
            self._create_image_export(filename, "PDF")
            
            # Show export success dialog
            self._show_export_success_dialog(filename, "PDF")
                
        except Exception as ex:
            logger.error(f"PDF export error: {ex}")
            self._show_export_error_dialog(f"Errore export PDF: {str(ex)}")
    
    def _get_export_directory(self):
        """Get the best export directory (Documents folder or fallback)"""
        import os
        
        try:
            # Try to get Documents folder
            if os.name == 'nt':  # Windows
                import winreg
                try:
                    with winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"Software\Microsoft\Windows\CurrentVersion\Explorer\Shell Folders") as key:
                        documents_path = winreg.QueryValueEx(key, "Personal")[0]
                except:
                    # Fallback for Windows
                    documents_path = os.path.join(os.path.expanduser("~"), "Documents")
            else:  # macOS/Linux
                documents_path = os.path.join(os.path.expanduser("~"), "Documents")
            
            # Create Agevolami PM subfolder
            export_dir = os.path.join(documents_path, "Agevolami PM", "Exports")
            
        except Exception as e:
            logger.warning(f"Could not access Documents folder: {e}, using fallback")
            # Fallback to local directory
            export_dir = os.path.join(os.getcwd(), "data", "exports")
        
        # Ensure directory exists
        if not os.path.exists(export_dir):
            os.makedirs(export_dir, exist_ok=True)
            
        return export_dir
    
    def _show_export_success_dialog(self, filename: str, format_type: str):
        """Show export success dialog with sharing options"""
        import os
        import subprocess
        import urllib.parse
        from datetime import datetime
        
        def close_dialog(e):
            dialog.open = False
            self.app.page.update()
        
        def open_file_location(e):
            """Open file location in file explorer"""
            try:
                folder_path = os.path.dirname(filename)
                if os.name == 'nt':  # Windows
                    try:
                        subprocess.run(['explorer', '/select,', filename], check=False)
                    except Exception:
                        subprocess.run(['explorer', folder_path], check=False)
                elif os.name == 'posix':  # macOS/Linux
                    if os.uname().sysname == 'Darwin':  # macOS
                        subprocess.run(['open', '-R', filename], check=False)
                    else:  # Linux
                        subprocess.run(['xdg-open', folder_path], check=False)
                close_dialog(e)
            except Exception as ex:
                logger.error(f"Error opening file location: {ex}")
                # Show error snackbar
                snack = ft.SnackBar(
                    content=ft.Text(f"❌ Impossibile aprire la cartella: {str(ex)}"),
                    bgcolor=ft.Colors.RED_600
                )
                self.app.page.snack_bar = snack
                snack.open = True
                self.app.page.update()
        
        def copy_file_path(e):
            """Copy file path to clipboard"""
            try:
                import pyperclip
                pyperclip.copy(filename)
                # Show success feedback
                snack = ft.SnackBar(
                    content=ft.Text("📋 Percorso file copiato negli appunti"),
                    bgcolor=ft.Colors.GREEN_600
                )
                self.app.page.snack_bar = snack
                snack.open = True
                self.app.page.update()
                close_dialog(e)
            except ImportError:
                # Fallback without pyperclip
                snack = ft.SnackBar(
                    content=ft.Text("📋 Installa 'pyperclip' per copiare il percorso"),
                    bgcolor=ft.Colors.ORANGE_600
                )
                self.app.page.snack_bar = snack
                snack.open = True
                self.app.page.update()
            except Exception as ex:
                logger.error(f"Error copying to clipboard: {ex}")
        
        def share_via_email(e):
            """Share file via email with attachment"""
            try:
                # Try to use Windows MAPI for file attachment
                if os.name == 'nt':  # Windows
                    try:
                        import win32api
                        import win32con
                        
                        # Use Windows MAPI to send email with attachment
                        subject = f"Gantt Chart Export - {format_type}"
                        body = f"Ciao,\n\nTi invio il Gantt Chart esportato da Agevolami PM.\n\nFile allegato: {os.path.basename(filename)}\nGenerato: {datetime.now().strftime('%d/%m/%Y alle %H:%M')}\n\nCordiali saluti"
                        
                        # Create MAPI session and send email with attachment
                        win32api.ShellExecute(
                            0,
                            'open',
                            'mailto:',
                            f'subject={subject}&body={body}&attach={filename}',
                            '',
                            win32con.SW_SHOWNORMAL
                        )
                        
                    except ImportError:
                        # Fallback: Try Outlook automation
                        try:
                            import win32com.client
                            
                            outlook = win32com.client.Dispatch("Outlook.Application")
                            mail = outlook.CreateItem(0)  # 0 = olMailItem
                            
                            mail.Subject = f"Gantt Chart Export - {format_type}"
                            mail.Body = f"Ciao,\n\nTi invio il Gantt Chart esportato da Agevolami PM.\n\nFile allegato: {os.path.basename(filename)}\nGenerato: {datetime.now().strftime('%d/%m/%Y alle %H:%M')}\n\nCordiali saluti"
                            mail.Attachments.Add(filename)
                            
                            mail.Display()  # Show the email for user to send
                            
                        except Exception:
                            # Final fallback: Open file location and use mailto
                            try:
                                subprocess.run(['explorer', '/select,', filename], check=False)
                            except Exception:
                                subprocess.run(['explorer', os.path.dirname(filename)], check=False)
                            
                            subject = f"Gantt Chart Export - {format_type}"
                            body = f"Ciao,\n\nTi invio il Gantt Chart esportato da Agevolami PM.\n\nFile: {os.path.basename(filename)}\nGenerato: {datetime.now().strftime('%d/%m/%Y alle %H:%M')}\n\nNOTA: Il file è stato selezionato in Esplora File. Allegalo manualmente all'email.\n\nCordiali saluti"
                            
                            mailto_url = f"mailto:?subject={urllib.parse.quote(subject)}&body={urllib.parse.quote(body)}"
                            os.startfile(mailto_url)
                            
                            # Show instruction to user
                            snack = ft.SnackBar(
                                content=ft.Text("📧 Email aperta e file selezionato. Allega manualmente il file."),
                                bgcolor=ft.Colors.BLUE_600,
                                duration=5000
                            )
                            self.app.page.snack_bar = snack
                            snack.open = True
                            self.app.page.update()
                
                else:  # macOS/Linux
                    # For macOS/Linux, open file location and email client
                    folder_path = os.path.dirname(filename)
                    if os.uname().sysname == 'Darwin':  # macOS
                        subprocess.run(['open', '-R', filename], check=False)
                        
                        subject = f"Gantt Chart Export - {format_type}"
                        body = f"Ciao,\n\nTi invio il Gantt Chart esportato da Agevolami PM.\n\nFile: {os.path.basename(filename)}\nGenerato: {datetime.now().strftime('%d/%m/%Y alle %H:%M')}\n\nCordiali saluti"
                        
                        mailto_url = f"mailto:?subject={urllib.parse.quote(subject)}&body={urllib.parse.quote(body)}"
                        subprocess.run(['open', mailto_url])
                    else:  # Linux
                        subprocess.run(['xdg-open', folder_path], check=False)
                        
                        subject = f"Gantt Chart Export - {format_type}"
                        body = f"Ciao,\n\nTi invio il Gantt Chart esportato da Agevolami PM.\n\nFile: {os.path.basename(filename)}\nGenerato: {datetime.now().strftime('%d/%m/%Y alle %H:%M')}\n\nCordiali saluti"
                        
                        mailto_url = f"mailto:?subject={urllib.parse.quote(subject)}&body={urllib.parse.quote(body)}"
                        subprocess.run(['xdg-open', mailto_url])
                
                close_dialog(e)
                
            except Exception as ex:
                logger.error(f"Error opening email client: {ex}")
                snack = ft.SnackBar(
                    content=ft.Text(f"❌ Errore apertura email: {str(ex)}"),
                    bgcolor=ft.Colors.RED_600
                )
                self.app.page.snack_bar = snack
                snack.open = True
                self.app.page.update()
        
        def share_via_whatsapp(e):
            """Share file via WhatsApp Desktop or Web"""
            try:
                message = f"📊 Gantt Chart da Agevolami PM\n\nFile: {os.path.basename(filename)}\nGenerato: {datetime.now().strftime('%d/%m/%Y alle %H:%M')}"
                
                whatsapp_opened = False
                
                if os.name == 'nt':  # Windows
                    # Try WhatsApp Desktop first
                    whatsapp_paths = [
                        os.path.expanduser(r"~\AppData\Local\WhatsApp\WhatsApp.exe"),
                        os.path.expanduser(r"~\AppData\Local\Programs\WhatsApp\WhatsApp.exe"),
                        r"C:\Program Files\WhatsApp\WhatsApp.exe",
                        r"C:\Program Files (x86)\WhatsApp\WhatsApp.exe"
                    ]
                    
                    for whatsapp_path in whatsapp_paths:
                        if os.path.exists(whatsapp_path):
                            try:
                                # Open WhatsApp Desktop
                                subprocess.Popen([whatsapp_path])
                                whatsapp_opened = True
                                
                                # Also open file location for easy drag-and-drop
                                try:
                                    subprocess.run(['explorer', '/select,', filename], check=False)
                                except Exception:
                                    # Fallback to opening the folder
                                    subprocess.run(['explorer', os.path.dirname(filename)], check=False)
                                
                                # Show instruction to user
                                snack = ft.SnackBar(
                                    content=ft.Text("💬 WhatsApp Desktop aperto e file selezionato. Trascina il file nella chat."),
                                    bgcolor=ft.Colors.GREEN_600,
                                    duration=6000
                                )
                                self.app.page.snack_bar = snack
                                snack.open = True
                                self.app.page.update()
                                break
                                
                            except Exception as ex:
                                logger.warning(f"Failed to open WhatsApp Desktop at {whatsapp_path}: {ex}")
                                continue
                    
                    # If WhatsApp Desktop not found, try WhatsApp Web
                    if not whatsapp_opened:
                        # Open file location first
                        try:
                            subprocess.run(['explorer', '/select,', filename], check=False)
                        except Exception:
                            # Fallback to opening the folder
                            subprocess.run(['explorer', os.path.dirname(filename)], check=False)
                        
                        # Create WhatsApp Web URL
                        whatsapp_url = f"https://web.whatsapp.com/send?text={urllib.parse.quote(message)}"
                        os.startfile(whatsapp_url)
                        
                        # Show instruction to user
                        snack = ft.SnackBar(
                            content=ft.Text("💬 WhatsApp Web aperto e file selezionato. Allega il file manualmente."),
                            bgcolor=ft.Colors.GREEN_600,
                            duration=6000
                        )
                        self.app.page.snack_bar = snack
                        snack.open = True
                        self.app.page.update()
                        whatsapp_opened = True
                
                elif os.name == 'posix':  # macOS/Linux
                    # For macOS, try WhatsApp Desktop
                    if os.uname().sysname == 'Darwin':  # macOS
                        whatsapp_app_path = "/Applications/WhatsApp.app"
                        if os.path.exists(whatsapp_app_path):
                            try:
                                subprocess.run(['open', '-a', 'WhatsApp'])
                                subprocess.run(['open', '-R', filename], check=False)
                                whatsapp_opened = True
                                
                                snack = ft.SnackBar(
                                    content=ft.Text("💬 WhatsApp aperto e file selezionato nel Finder."),
                                    bgcolor=ft.Colors.GREEN_600,
                                    duration=6000
                                )
                                self.app.page.snack_bar = snack
                                snack.open = True
                                self.app.page.update()
                            except Exception:
                                pass
                    
                    # Fallback to WhatsApp Web for macOS/Linux
                    if not whatsapp_opened:
                        folder_path = os.path.dirname(filename)
                        whatsapp_url = f"https://web.whatsapp.com/send?text={urllib.parse.quote(message)}"
                        
                        if os.uname().sysname == 'Darwin':  # macOS
                            subprocess.run(['open', '-R', filename], check=False)
                            subprocess.run(['open', whatsapp_url])
                        else:  # Linux
                            subprocess.run(['xdg-open', folder_path], check=False)
                            subprocess.run(['xdg-open', whatsapp_url])
                        
                        whatsapp_opened = True
                
                if not whatsapp_opened:
                    raise Exception("WhatsApp non trovato sul sistema")
                
                close_dialog(e)
                
            except Exception as ex:
                logger.error(f"Error opening WhatsApp: {ex}")
                snack = ft.SnackBar(
                    content=ft.Text(f"❌ Errore apertura WhatsApp: {str(ex)}"),
                    bgcolor=ft.Colors.RED_600
                )
                self.app.page.snack_bar = snack
                snack.open = True
                self.app.page.update()
        
        # Get file info
        file_size = os.path.getsize(filename) if os.path.exists(filename) else 0
        file_size_mb = file_size / (1024 * 1024)
        
        # Create dialog
        dialog = ft.AlertDialog(
            title=ft.Row([
                ft.Icon(ft.Icons.CHECK_CIRCLE, color=ft.Colors.GREEN_600, size=24),
                ft.Text("Export Completato!", weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN_600)
            ], spacing=8),
            content=ft.Container(
                content=ft.Column([
                    # File info
                    ft.Container(
                        content=ft.Column([
                            ft.Row([
                                ft.Icon(ft.Icons.DESCRIPTION, size=16, color=ft.Colors.BLUE_600),
                                ft.Text("Informazioni File", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_600)
                            ], spacing=6),
                            ft.Text(f"Nome: {os.path.basename(filename)}", size=12, color=ft.Colors.GREY_700),
                            ft.Text(f"Formato: {format_type}", size=12, color=ft.Colors.GREY_700),
                            ft.Text(f"Dimensione: {file_size_mb:.2f} MB", size=12, color=ft.Colors.GREY_700),
                            ft.Text(f"Percorso: {filename}", size=10, color=ft.Colors.GREY_500, selectable=True)
                        ], spacing=4),
                        padding=ft.padding.all(12),
                        bgcolor=ft.Colors.BLUE_50,
                        border_radius=8,
                        border=ft.border.all(1, ft.Colors.BLUE_200)
                    ),
                    
                    ft.Container(height=10),
                    
                    # Action buttons
                    ft.Text("Azioni Disponibili:", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_700),
                    
                    ft.Row([
                        ft.ElevatedButton(
                            content=ft.Row([
                                ft.Icon(ft.Icons.FOLDER_OPEN, size=16),
                                ft.Text("Apri Cartella", size=12)
                            ], spacing=4, tight=True),
                            on_click=open_file_location,
                            bgcolor=ft.Colors.BLUE_600,
                            color=ft.Colors.WHITE
                        ),
                        ft.ElevatedButton(
                            content=ft.Row([
                                ft.Icon(ft.Icons.COPY, size=16),
                                ft.Text("Copia Percorso", size=12)
                            ], spacing=4, tight=True),
                            on_click=copy_file_path,
                            bgcolor=ft.Colors.GREY_600,
                            color=ft.Colors.WHITE
                        )
                    ], spacing=8),
                    
                    ft.Row([
                        ft.ElevatedButton(
                            content=ft.Row([
                                ft.Icon(ft.Icons.EMAIL, size=16),
                                ft.Text("Condividi Email", size=12)
                            ], spacing=4, tight=True),
                            on_click=share_via_email,
                            bgcolor=ft.Colors.GREEN_600,
                            color=ft.Colors.WHITE
                        ),
                        ft.ElevatedButton(
                            content=ft.Row([
                                ft.Icon(ft.Icons.SEND, size=16),
                                ft.Text("SMTP Email", size=12)
                            ], spacing=4, tight=True),
                            on_click=lambda e: self._show_smtp_email_dialog(filename, format_type),
                            bgcolor=ft.Colors.INDIGO_600,
                            color=ft.Colors.WHITE
                        )
                    ], spacing=8),
                    
                    ft.Row([
                        ft.ElevatedButton(
                            content=ft.Row([
                                ft.Icon(ft.Icons.CHAT, size=16),
                                ft.Text("WhatsApp", size=12)
                            ], spacing=4, tight=True),
                            on_click=share_via_whatsapp,
                            bgcolor=ft.Colors.GREEN_700,
                            color=ft.Colors.WHITE
                        )
                    ], spacing=8)
                ], spacing=8),
                width=500
            ),
            actions=[
                ft.TextButton("Chiudi", on_click=close_dialog)
            ]
        )
        
        self.app.page.overlay.append(dialog)
        dialog.open = True
        self.app.page.update()
    
    def _show_smtp_email_dialog(self, filename: str, format_type: str):
        """Show SMTP email dialog for direct email sending"""
        import os
        from datetime import datetime
        
        # References for form fields
        recipient_ref = ft.Ref[ft.TextField]()
        subject_ref = ft.Ref[ft.TextField]()
        message_ref = ft.Ref[ft.TextField]()
        
        def close_dialog(e):
            self.app.page.close(smtp_dialog)
        
        def send_via_smtp(e):
            """Send email via SMTP with attachment"""
            try:
                recipient = recipient_ref.current.value
                subject = subject_ref.current.value
                message = message_ref.current.value
                
                # Basic validation
                if not recipient or not subject:
                    snack = ft.SnackBar(
                        content=ft.Text("❌ Destinatario e oggetto sono obbligatori"),
                        bgcolor=ft.Colors.RED_600
                    )
                    self.app.page.snack_bar = snack
                    snack.open = True
                    self.app.page.update()
                    return
                
                # Validate email format
                import re
                email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
                if not re.match(email_pattern, recipient):
                    snack = ft.SnackBar(
                        content=ft.Text("❌ Formato email non valido"),
                        bgcolor=ft.Colors.RED_600
                    )
                    self.app.page.snack_bar = snack
                    snack.open = True
                    self.app.page.update()
                    return
                
                # Show sending feedback
                snack = ft.SnackBar(
                    content=ft.Text("📧 Invio email in corso..."),
                    bgcolor=ft.Colors.BLUE_600
                )
                self.app.page.snack_bar = snack
                snack.open = True
                self.app.page.update()
                
                # Get email service
                from core.services.email_service import EmailService
                from core.config.app_config import AppConfig
                
                # Load current settings
                import json
                from pathlib import Path
                
                settings_file = Path('data/settings.json')
                email_settings = {}
                
                if settings_file.exists():
                    try:
                        with open(settings_file, 'r', encoding='utf-8') as f:
                            settings = json.load(f)
                            email_settings = settings.get('email', {})
                    except Exception as e:
                        logger.error(f"Error loading email settings: {e}")
                
                # Check if SMTP is configured
                if not email_settings.get('server') or not email_settings.get('username'):
                    snack = ft.SnackBar(
                        content=ft.Text("❌ SMTP non configurato. Vai alle Impostazioni per configurarlo."),
                        bgcolor=ft.Colors.RED_600,
                        duration=5000
                    )
                    self.app.page.snack_bar = snack
                    snack.open = True
                    self.app.page.update()
                    return
                
                # Initialize email service
                config = AppConfig()
                email_service = EmailService(config)
                
                # Update email service with current settings
                email_service.smtp_config.update({
                    'smtp_server': email_settings.get('server', ''),
                    'smtp_port': email_settings.get('port', 587),
                    'smtp_username': email_settings.get('username', ''),
                    'smtp_password': email_settings.get('password', ''),
                    'smtp_use_tls': email_settings.get('use_tls', True),
                    'from_name': email_settings.get('sender_name', 'Agevolami PM'),
                    'from_email': email_settings.get('sender_email', email_settings.get('username', '')),
                    'enabled': True
                })
                
                # Test connection first
                if not email_service.test_connection():
                    snack = ft.SnackBar(
                        content=ft.Text("❌ Impossibile connettersi al server SMTP. Verifica le impostazioni."),
                        bgcolor=ft.Colors.RED_600,
                        duration=5000
                    )
                    self.app.page.snack_bar = snack
                    snack.open = True
                    self.app.page.update()
                    return
                
                # Send email with attachment
                success = email_service.send_email_with_attachment(
                    to_email=recipient,
                    subject=subject,
                    body=message,
                    attachment_path=filename
                )
                
                if success:
                    # Close dialog and show success
                    close_dialog(e)
                    
                    snack = ft.SnackBar(
                        content=ft.Text(f"✅ Email inviata con successo a {recipient}"),
                        bgcolor=ft.Colors.GREEN_600,
                        duration=5000
                    )
                    self.app.page.snack_bar = snack
                    snack.open = True
                    self.app.page.update()
                    
                    logger.info(f"SMTP email sent successfully to {recipient}")
                else:
                    snack = ft.SnackBar(
                        content=ft.Text("❌ Errore durante l'invio dell'email"),
                        bgcolor=ft.Colors.RED_600,
                        duration=5000
                    )
                    self.app.page.snack_bar = snack
                    snack.open = True
                    self.app.page.update()
                    
            except Exception as ex:
                logger.error(f"SMTP email error: {ex}")
                snack = ft.SnackBar(
                    content=ft.Text(f"❌ Errore SMTP: {str(ex)}"),
                    bgcolor=ft.Colors.RED_600,
                    duration=5000
                )
                self.app.page.snack_bar = snack
                snack.open = True
                self.app.page.update()
        
        # Get file info
        file_size = os.path.getsize(filename) if os.path.exists(filename) else 0
        file_size_mb = file_size / (1024 * 1024)
        
        # Create default email content
        default_subject = f"Gantt Chart Export - {format_type} - {datetime.now().strftime('%d/%m/%Y')}"
        default_message = f"""Ciao,

Ti invio il Gantt Chart esportato da Agevolami PM.

Dettagli file:
- Nome: {os.path.basename(filename)}
- Formato: {format_type}
- Dimensione: {file_size_mb:.2f} MB
- Generato: {datetime.now().strftime('%d/%m/%Y alle %H:%M')}

Il file è allegato a questa email.

Cordiali saluti,
Agevolami PM"""
        
        # Create dialog
        smtp_dialog = ft.AlertDialog(
            title=ft.Row([
                ft.Icon(ft.Icons.SEND, color=ft.Colors.INDIGO_600, size=24),
                ft.Text("Invia Email SMTP", weight=ft.FontWeight.BOLD, color=ft.Colors.INDIGO_600)
            ], spacing=8),
            content=ft.Container(
                content=ft.Column([
                    # File info
                    ft.Container(
                        content=ft.Column([
                            ft.Row([
                                ft.Icon(ft.Icons.ATTACH_FILE, size=16, color=ft.Colors.BLUE_600),
                                ft.Text("File Allegato", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_600)
                            ], spacing=6),
                            ft.Text(f"Nome: {os.path.basename(filename)}", size=12, color=ft.Colors.GREY_700),
                            ft.Text(f"Formato: {format_type}", size=12, color=ft.Colors.GREY_700),
                            ft.Text(f"Dimensione: {file_size_mb:.2f} MB", size=12, color=ft.Colors.GREY_700),
                        ], spacing=4),
                        padding=ft.padding.all(12),
                        bgcolor=ft.Colors.BLUE_50,
                        border_radius=8,
                        border=ft.border.all(1, ft.Colors.BLUE_200)
                    ),
                    
                    ft.Container(height=10),
                    
                    # Email form
                    ft.Text("Componi Email:", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_700),
                    
                    ft.TextField(
                        ref=recipient_ref,
                        label="Destinatario Email *",
                        hint_text="<EMAIL>",
                        prefix_icon=ft.Icons.PERSON,
                        text_size=14,
                        border_color=ft.Colors.INDIGO_200,
                        focused_border_color=ft.Colors.INDIGO_600
                    ),
                    
                    ft.TextField(
                        ref=subject_ref,
                        label="Oggetto *",
                        value=default_subject,
                        prefix_icon=ft.Icons.SUBJECT,
                        text_size=14,
                        border_color=ft.Colors.INDIGO_200,
                        focused_border_color=ft.Colors.INDIGO_600
                    ),
                    
                    ft.TextField(
                        ref=message_ref,
                        label="Messaggio",
                        value=default_message,
                        prefix_icon=ft.Icons.MESSAGE,
                        text_size=14,
                        multiline=True,
                        max_lines=8,
                        border_color=ft.Colors.INDIGO_200,
                        focused_border_color=ft.Colors.INDIGO_600
                    ),
                    
                    # Info note
                    ft.Container(
                        content=ft.Row([
                            ft.Icon(ft.Icons.INFO, size=16, color=ft.Colors.ORANGE_600),
                            ft.Text("Utilizza le impostazioni SMTP configurate nell'applicazione", 
                                   size=12, color=ft.Colors.ORANGE_600, expand=True)
                        ], spacing=6),
                        padding=ft.padding.all(8),
                        bgcolor=ft.Colors.ORANGE_50,
                        border_radius=6,
                        border=ft.border.all(1, ft.Colors.ORANGE_200)
                    )
                ], spacing=8, scroll=ft.ScrollMode.AUTO),
                width=500,
                height=500
            ),
            actions=[
                ft.Row([
                    ft.TextButton(
                        "Annulla",
                        on_click=close_dialog,
                        style=ft.ButtonStyle(color=ft.Colors.GREY_600)
                    ),
                    ft.ElevatedButton(
                        content=ft.Row([
                            ft.Icon(ft.Icons.SEND, size=16),
                            ft.Text("Invia Email", size=12)
                        ], spacing=4, tight=True),
                        on_click=send_via_smtp,
                        bgcolor=ft.Colors.INDIGO_600,
                        color=ft.Colors.WHITE
                    )
                ], alignment=ft.MainAxisAlignment.END, spacing=10)
            ]
        )
        
        self.app.page.open(smtp_dialog)
    
    def _show_export_error_dialog(self, error_message: str):
        """Show export error dialog"""
        def close_dialog(e):
            dialog.open = False
            self.app.page.update()
        
        dialog = ft.AlertDialog(
            title=ft.Row([
                ft.Icon(ft.Icons.ERROR, color=ft.Colors.RED_600, size=24),
                ft.Text("Errore Export", weight=ft.FontWeight.BOLD, color=ft.Colors.RED_600)
            ], spacing=8),
            content=ft.Container(
                content=ft.Column([
                    ft.Text("Si è verificato un errore durante l'esportazione:", size=14),
                    ft.Container(
                        content=ft.Text(error_message, size=12, color=ft.Colors.RED_700, selectable=True),
                        padding=ft.padding.all(12),
                        bgcolor=ft.Colors.RED_50,
                        border_radius=8,
                        border=ft.border.all(1, ft.Colors.RED_200)
                    )
                ], spacing=12),
                width=400
            ),
            actions=[
                ft.TextButton("Chiudi", on_click=close_dialog)
            ]
        )
        
        self.app.page.overlay.append(dialog)
        dialog.open = True
        self.app.page.update()
    
    def build(self) -> ft.Container:
        """🏗️ Build the complete Gantt chart view"""
        
        # Load initial data
        self.load_data()
        
        # Create header
        header = ft.Container(
            content=ft.Row([
                ft.Icon(ft.Icons.TIMELINE, size=28, color=ft.Colors.BLUE_600),
                ft.Text("🚀 Native Gantt Chart", size=24, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_800),
                ft.Container(expand=True),
                ft.Container(content=self._create_stats(), ref=self)
            ]),
            padding=ft.padding.all(20),
            bgcolor=ft.Colors.WHITE,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.GREY_200)
        )
        
        # Store stats container reference
        self.stats_container = header.content.controls[3]
        
        # Create controls
        controls = self._create_controls()
        
        # Create main chart
        timeline = self._create_timeline_header()
        labels = self._create_labels_column()
        chart = self._create_chart_area()
        
        # Create scrollable chart area
        scrollable_chart = ft.Container(
            content=ft.Column([timeline, chart], spacing=0),
            width=self.chart_width,
            height=min(600, len(self.filtered_tasks) * self.row_height + 60)  # Max height with scroll
        )
        
        self.chart_container = ft.Container(
            content=ft.Row([
                labels,
                ft.Container(
                    content=ft.Row([scrollable_chart], scroll=ft.ScrollMode.AUTO),
                    expand=True
                )
            ], spacing=0),
            border_radius=5,
            border=ft.border.all(1, ft.Colors.GREY_300),
            bgcolor=ft.Colors.WHITE
        )
        
        # Build main layout
        return ft.Container(
            content=ft.Column([
                header,
                controls,
                self.chart_container,
                ft.Container(height=20)  # spacer
            ], spacing=15, scroll=ft.ScrollMode.AUTO),
            padding=ft.padding.all(20),
            bgcolor=ft.Colors.GREY_50,
            expand=True
        ) 