#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Incentives Settings Section
Dedicated configuration interface for Italian financial incentives monitoring
"""

import flet as ft
import json
from typing import Dict, Any, List, Optional
from pathlib import Path

from core.utils.logger import get_logger

logger = get_logger(__name__)

class IncentivesSection:
    """Settings section for incentives monitoring configuration"""
    
    def __init__(self, app_instance):
        self.app = app_instance
        self.settings = self._load_settings()
        
        # UI references for dynamic updates
        self.enabled_switch = ft.Ref[ft.Switch]()
        self.frequency_dropdown = ft.Ref[ft.Dropdown]()
        self.keywords_field = ft.Ref[ft.TextField]()
        self.api_key_field = ft.Ref[ft.TextField]()
        self.model_dropdown = ft.Ref[ft.Dropdown]()
        self.llm_enabled_switch = ft.Ref[ft.Switch]()
        self.email_enabled_switch = ft.Ref[ft.Switch]()
        self.notification_email_field = ft.Ref[ft.TextField]()
        self.connection_status = ft.Ref[ft.Text]()
    
    def _load_settings(self) -> Dict[str, Any]:
        """Load incentives settings with unified approach"""
        try:
            # Primary: Try main settings manager
            if hasattr(self.app, 'settings_manager') and self.app.settings_manager:
                settings = self.app.settings_manager.settings.get('incentives', {})
                if settings:
                    logger.info("Loaded incentives settings from main settings manager")
                    return settings
            
            # Fallback: JSON file (for migration)
            config_file = Path("data/incentives_config.json")
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    logger.info(f"Loaded incentives settings from JSON file: {config_file}")
                    
                    # Auto-migrate to main settings if available
                    if hasattr(self.app, 'settings_manager') and self.app.settings_manager:
                        logger.info("Auto-migrating settings from JSON to main settings manager")
                        self.app.settings_manager.settings['incentives'] = settings
                        self.app.settings_manager._save_settings()
                    
                    return settings
            
            # Default configuration
            return self._get_default_config()
            
        except Exception as e:
            logger.error(f"Error loading incentives settings: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default incentives configuration"""
        return {
            'enabled': False,
            'frequency': 'weekly',
            'keywords': ['incentivi', 'finanziamenti', 'bandi', 'agevolazioni', 'sostenibili'],
            'openrouter_api_key': '',
            'llm_model': 'deepseek/deepseek-r1-0528-qwen3-8b:free',
            'llm_api_url': 'https://openrouter.ai/api/v1',
            'llm_enabled': False,
            'email_notifications': True,
            'notification_email': '',
            'scraping_method': 'hybrid',
            'validation': {
                'min_title_length': 5,
                'max_title_length': 300,
                'min_description_length': 5,
                'max_description_length': 2000,
                'min_relevance_score': 0.1,
                'max_duplicate_similarity': 0.95
            },
            'retry': {
                'max_retries': 3,
                'base_delay': 1,
                'max_delay': 60,
                'exponential_base': 2,
                'jitter': True
            },
            'websites': [
                {
                    'name': 'MIMIT - Ministero Imprese',
                    'url': 'https://www.mimit.gov.it',
                    'enabled': True,
                    'search_paths': ['/it/incentivi-mise', '/it/notizie-stampa', '/it/per-l-impresa']
                },
                {
                    'name': 'Invitalia',
                    'url': 'https://www.invitalia.it',
                    'enabled': True,
                    'search_paths': ['/per-le-imprese', '/news', '/per-chi-vuole-fare-impresa']
                },
                {
                    'name': 'SIMEST',
                    'url': 'https://www.simest.it',
                    'enabled': True,
                    'search_paths': ['/per-le-imprese/finanziamenti-agevolati', '/media']
                }
            ],
            'custom_models': {}
        }
    
    def build(self) -> ft.Container:
        """Build the incentives settings section"""
        return ft.Container(
            content=ft.Column([
                self._create_section_header(),
                self._create_monitoring_config(),
                self._create_llm_config(),
                self._create_notification_config(),
                self._create_websites_preview(),
                self._create_action_buttons()
            ], spacing=16),
            padding=ft.padding.all(20),
            bgcolor=ft.Colors.WHITE,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.GREY_200)
        )
    
    def _create_section_header(self) -> ft.Container:
        """Create section header with title and description"""
        return ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Icon(
                        ft.Icons.TRENDING_UP,
                        size=32,
                        color=ft.Colors.PURPLE_600
                    ),
                    ft.Column([
                        ft.Text(
                            "Monitoraggio Incentivi Automatico",
                            size=20,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.GREY_800
                        ),
                        ft.Text(
                            "Configurazione del sistema di monitoraggio automatico per incentivi finanziari italiani",
                            size=14,
                            color=ft.Colors.GREY_600
                        )
                    ], spacing=4, expand=True)
                ], spacing=12),
                
                ft.Divider(color=ft.Colors.GREY_300)
            ], spacing=12),
            padding=ft.padding.only(bottom=8)
        )
    
    def _create_monitoring_config(self) -> ft.Container:
        """Create monitoring configuration card"""
        return ft.Container(
            content=ft.Column([
                ft.Text(
                    "🔍 Configurazione Monitoraggio",
                    size=16,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.GREY_800
                ),
                
                ft.Row([
                    ft.Column([
                        ft.Text("Monitoraggio attivo:", size=12, color=ft.Colors.GREY_600),
                        ft.Switch(
                            value=self.settings.get('enabled', False),
                            on_change=lambda e: self._update_setting('enabled', e.control.value),
                            ref=self.enabled_switch
                        )
                    ], spacing=4),
                    
                    ft.Container(width=20),
                    
                    ft.Column([
                        ft.Text("Frequenza controllo:", size=12, color=ft.Colors.GREY_600),
                        ft.Dropdown(
                            width=160,
                            options=[
                                ft.dropdown.Option("daily", "Giornaliero"),
                                ft.dropdown.Option("weekly", "Settimanale"),
                                ft.dropdown.Option("monthly", "Mensile")
                            ],
                            value=self.settings.get('frequency', 'weekly'),
                            on_change=lambda e: self._update_setting('frequency', e.control.value),
                            ref=self.frequency_dropdown
                        )
                    ], spacing=4)
                ], alignment=ft.MainAxisAlignment.START),
                
                ft.Column([
                    ft.Text("Parole chiave di ricerca:", size=12, color=ft.Colors.GREY_600),
                    ft.TextField(
                        hint_text="incentivi, finanziamenti, bandi, agevolazioni...",
                        value=', '.join(self.settings.get('keywords', [])) if isinstance(self.settings.get('keywords'), list) else self.settings.get('keywords', ''),
                        multiline=False,
                        on_change=lambda e: self._update_keywords(e.control.value),
                        ref=self.keywords_field
                    )
                ], spacing=4)
            ], spacing=12),
            padding=ft.padding.all(16),
            bgcolor=ft.Colors.BLUE_50,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.BLUE_200)
        )
    
    def _create_llm_config(self) -> ft.Container:
        """Create LLM configuration card"""
        return ft.Container(
            content=ft.Column([
                ft.Text(
                    "🤖 Configurazione LLM (OpenRouter)",
                    size=16,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.GREY_800
                ),
                
                ft.Row([
                    ft.Column([
                        ft.Text("LLM abilitato:", size=12, color=ft.Colors.GREY_600),
                        ft.Switch(
                            value=self.settings.get('llm_enabled', False),
                            on_change=lambda e: self._update_setting('llm_enabled', e.control.value),
                            ref=self.llm_enabled_switch
                        )
                    ], spacing=4),
                    
                    ft.Container(width=20),
                    
                    ft.Column([
                        ft.Text("Stato connessione:", size=12, color=ft.Colors.GREY_600),
                        ft.Text(
                            self._get_connection_status(),
                            size=12,
                            weight=ft.FontWeight.BOLD,
                            color=self._get_connection_color(),
                            ref=self.connection_status
                        )
                    ], spacing=4)
                ]),
                
                ft.Row([
                    ft.Column([
                        ft.Text("API Key:", size=12, color=ft.Colors.GREY_600),
                        ft.TextField(
                            hint_text="sk-or-v1-...",
                            width=300,
                            password=True,
                            value=self.settings.get('openrouter_api_key', ''),
                            on_change=lambda e: self._update_setting('openrouter_api_key', e.control.value),
                            ref=self.api_key_field
                        )
                    ], spacing=4),
                    
                    ft.Container(width=20),
                    
                    ft.Column([
                        ft.Text("Modello (cerca o inserisci custom):", size=12, color=ft.Colors.GREY_600),
                        ft.Container(
                            content=ft.AutoComplete(
                                suggestions=[
                                    ft.AutoCompleteSuggestion(key=model_id, value=display_name)
                                    for model_id, display_name in self._get_model_list()
                                ],
                                on_select=self._on_model_selected,
                                ref=self.model_dropdown
                            ),
                            width=250
                        )
                    ], spacing=4)
                ]),
                
                ft.Row([
                    ft.ElevatedButton(
                        text="Test Connessione",
                        icon=ft.Icons.WIFI_TETHERING,
                        on_click=self._test_llm_connection
                    )
                ]),
                
                ft.Text(
                    "💡 Digita nel box 'Modello' per cercare o aggiungere un modello custom.",
                    size=12,
                    color=ft.Colors.GREY_500,
                    italic=True
                )
            ], spacing=12),
            padding=ft.padding.all(16),
            bgcolor=ft.Colors.GREEN_50,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.GREEN_200)
        )
    
    def _create_notification_config(self) -> ft.Container:
        """Create notification configuration card"""
        return ft.Container(
            content=ft.Column([
                ft.Text(
                    "📧 Configurazione Notifiche",
                    size=16,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.GREY_800
                ),
                
                ft.Row([
                    ft.Column([
                        ft.Text("Notifiche email:", size=12, color=ft.Colors.GREY_600),
                        ft.Switch(
                            value=self.settings.get('email_notifications', True),
                            on_change=lambda e: self._update_setting('email_notifications', e.control.value),
                            ref=self.email_enabled_switch
                        )
                    ], spacing=4),
                    
                    ft.Container(width=20),
                    
                    ft.Column([
                        ft.Text("Email di notifica:", size=12, color=ft.Colors.GREY_600),
                        ft.TextField(
                            hint_text="<EMAIL>",
                            width=250,
                            value=self.settings.get('notification_email', ''),
                            on_change=lambda e: self._update_setting('notification_email', e.control.value),
                            ref=self.notification_email_field
                        )
                    ], spacing=4)
                ])
            ], spacing=12),
            padding=ft.padding.all(16),
            bgcolor=ft.Colors.ORANGE_50,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.ORANGE_200)
        )
    
    def _create_websites_preview(self) -> ft.Container:
        """Create websites preview card"""
        websites = self.settings.get('websites', [])
        enabled_count = sum(1 for w in websites if w.get('enabled', True))
        
        return ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Text(
                        "🌐 Siti Web Monitorati",
                        size=16,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.GREY_800
                    ),
                    ft.Container(expand=True),
                    ft.Text(
                        f"{enabled_count}/{len(websites)} attivi",
                        size=12,
                        color=ft.Colors.GREY_600
                    )
                ]),
                
                ft.Container(
                    content=ft.Column([
                        ft.ListTile(
                            leading=ft.Icon(
                                ft.Icons.LANGUAGE,
                                color=ft.Colors.GREEN_600 if w.get('enabled', True) else ft.Colors.GREY_400
                            ),
                            title=ft.Text(w.get('name', 'Sito Sconosciuto')),
                            subtitle=ft.Text(w.get('url', '')),
                            trailing=ft.Icon(
                                ft.Icons.CHECK_CIRCLE if w.get('enabled', True) else ft.Icons.CANCEL,
                                color=ft.Colors.GREEN_600 if w.get('enabled', True) else ft.Colors.GREY_400
                            )
                        ) for w in websites[:3]  # Show only first 3
                    ] + ([
                        ft.Text(f"... e altri {len(websites) - 3} siti", size=12, color=ft.Colors.GREY_500)
                    ] if len(websites) > 3 else []), spacing=4),
                    height=120,
                    border=ft.border.all(1, ft.Colors.GREY_300),
                    border_radius=8,
                    padding=ft.padding.all(8)
                ),
                
                ft.ElevatedButton(
                    text="Gestisci Siti Web",
                    icon=ft.Icons.SETTINGS,
                    on_click=self._show_websites_manager
                )
            ], spacing=12),
            padding=ft.padding.all(16),
            bgcolor=ft.Colors.PURPLE_50,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.PURPLE_200)
        )
    
    def _create_action_buttons(self) -> ft.Container:
        """Create action buttons"""
        return ft.Container(
            content=ft.Row([
                ft.ElevatedButton(
                    text="Salva Configurazione",
                    icon=ft.Icons.SAVE,
                    style=ft.ButtonStyle(
                        bgcolor=ft.Colors.GREEN_600,
                        color=ft.Colors.WHITE
                    ),
                    on_click=self._save_settings
                ),
                ft.Container(width=12),
                ft.ElevatedButton(
                    text="Test Sistema",
                    icon=ft.Icons.PLAY_ARROW,
                    on_click=self._test_system
                ),
                ft.Container(width=12),
                ft.ElevatedButton(
                    text="Ripristina Default",
                    icon=ft.Icons.RESTORE,
                    on_click=self._restore_defaults
                )
            ], alignment=ft.MainAxisAlignment.CENTER),
            padding=ft.padding.symmetric(vertical=8)
        )
    
    def _get_model_list(self) -> List[tuple]:
        """Get list of available models as tuples (id, display_name)"""
        default_models = [
            ("google/gemini-flash-1.5", "Google Gemini Flash 1.5"),
            ("microsoft/wizardlm-2-8x22b", "Microsoft WizardLM-2 8x22B"),
            ("meta-llama/llama-3-8b-instruct", "Meta Llama 3 8B Instruct"),
            ("deepseek/deepseek-chat", "DeepSeek Chat"),
            # Free Models
            ("google/gemma-2-9b-it:free", "Google Gemma 2 9B (Free, Italian)"),
            ("mistralai/mistral-7b-instruct:free", "Mistral 7B Instruct (Free)"),
            ("nousresearch/nous-hermes-2-mixtral-8x7b-dpo:free", "Nous Hermes 2 Mixtral (Free)"),
            ("openai/gpt-4o-mini:free", "OpenAI GPT-4o Mini (Free)"),
        ]
        
        # Add custom models
        custom_models = self.settings.get('custom_models', {})
        for model_id, display_name in custom_models.items():
            default_models.append((model_id, f"{display_name} (Custom)"))
        
        return default_models
    
    def _on_model_selected(self, e):
        """Handle model selection from AutoComplete"""
        try:
            if e.selection and hasattr(e.selection, 'key'):
                model_id = e.selection.key
                self._update_setting('llm_model', model_id)
                logger.info(f"Selected model: {model_id}")
            elif e.control.value:
                # User typed a custom model
                model_id = e.control.value.strip()
                if model_id:
                    self._add_custom_model(model_id)
                    self._update_setting('llm_model', model_id)
                    
        except Exception as ex:
            logger.error(f"Error handling model selection: {ex}")
            self._show_error(f"❌ Errore selezione modello: {ex}")
    
    def _add_custom_model(self, model_id: str):
        """Add a custom model to the list"""
        try:
            custom_models = self.settings.get('custom_models', {})
            if model_id not in custom_models:
                custom_models[model_id] = model_id  # Use ID as display name
                self.settings['custom_models'] = custom_models
                logger.info(f"Added custom model: {model_id}")
                
                # Refresh AutoComplete suggestions
                if self.model_dropdown.current:
                    self.model_dropdown.current.suggestions = [
                        ft.AutoCompleteSuggestion(key=model_id, value=display_name)
                        for model_id, display_name in self._get_model_list()
                    ]
                    self.model_dropdown.current.update()
                    
        except Exception as e:
            logger.error(f"Error adding custom model: {e}")

    def _get_llm_model_options(self) -> List[ft.dropdown.Option]:
        """Get available LLM model options (kept for compatibility)"""
        model_list = self._get_model_list()
        
        options = []
        for model_id, display_name in model_list:
            options.append(ft.dropdown.Option(model_id, display_name))
        
        return options
    
    def _get_connection_status(self) -> str:
        """Get current connection status"""
        if not self.settings.get('llm_enabled', False):
            return "Disabilitato"
        elif not self.settings.get('openrouter_api_key', ''):
            return "API Key mancante"
        else:
            return "Configurato"
    
    def _get_connection_color(self) -> str:
        """Get connection status color"""
        status = self._get_connection_status()
        if status == "Disabilitato":
            return ft.Colors.GREY_600
        elif status == "API Key mancante":
            return ft.Colors.RED_600
        else:
            return ft.Colors.GREEN_600
    
    def _update_setting(self, key: str, value: Any):
        """Update a setting value"""
        try:
            self.settings[key] = value
            self._save_settings_silent()
            
            # Update connection status if relevant
            if key in ['llm_enabled', 'openrouter_api_key'] and self.connection_status.current:
                self.connection_status.current.value = self._get_connection_status()
                self.connection_status.current.color = self._get_connection_color()
                self.connection_status.current.update()
                
            logger.info(f"Updated incentives setting: {key} = {value}")
            
        except Exception as e:
            logger.error(f"Error updating setting {key}: {e}")
            self._show_error(f"Errore aggiornamento impostazione: {e}")
    
    def _update_keywords(self, value: str):
        """Update keywords setting"""
        try:
            # Convert comma-separated string to list
            keywords = [k.strip() for k in value.split(',') if k.strip()]
            self._update_setting('keywords', keywords)
        except Exception as e:
            logger.error(f"Error updating keywords: {e}")
    
    def _save_settings_silent(self):
        """Save settings without user notification"""
        try:
            # Save to main settings manager
            if hasattr(self.app, 'settings_manager') and self.app.settings_manager:
                self.app.settings_manager.settings['incentives'] = self.settings
                self.app.settings_manager._save_settings()
            
            # Also save to JSON for backward compatibility
            config_file = Path("data/incentives_config.json")
            config_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"Error saving settings silently: {e}")
    
    def _save_settings(self, e=None):
        """Save settings with user feedback"""
        try:
            self._save_settings_silent()
            self._show_success("✅ Configurazione salvata con successo!")
            logger.info("Incentives settings saved successfully")
            
        except Exception as ex:
            logger.error(f"Error saving settings: {ex}")
            self._show_error(f"❌ Errore salvataggio: {ex}")
    
    def _test_llm_connection(self, e):
        """Test LLM connection"""
        try:
            from core.services.llm_service import LLMService
            
            if not self.settings.get('openrouter_api_key', ''):
                self._show_error("❌ API Key non configurata!")
                return
            
            if not self.settings.get('llm_model', ''):
                self._show_error("❌ Modello non selezionato!")
                return
            
            # Test connection
            llm_service = LLMService(self.settings)
            
            if llm_service.test_connection():
                self._show_success("✅ Connessione LLM riuscita!")
                if self.connection_status.current:
                    self.connection_status.current.value = "Connesso"
                    self.connection_status.current.color = ft.Colors.GREEN_600
                    self.connection_status.current.update()
            else:
                self._show_error("❌ Test di connessione fallito!")
                if self.connection_status.current:
                    self.connection_status.current.value = "Errore connessione"
                    self.connection_status.current.color = ft.Colors.RED_600
                    self.connection_status.current.update()
                    
        except Exception as ex:
            logger.error(f"Error testing LLM connection: {ex}")
            self._show_error(f"❌ Errore nel test: {ex}")
    
    def _test_system(self, e):
        """Test the entire incentives system"""
        try:
            self._show_info("🔄 Test del sistema in corso...")
            
            # Test monitoring service initialization
            from core.services.incentives_monitoring_service import IncentivesMonitoringService
            
            monitoring_service = IncentivesMonitoringService(
                self.app.db_manager if hasattr(self.app, 'db_manager') else None,
                {'incentives': self.settings},
                self.app.email_service if hasattr(self.app, 'email_service') else None
            )
            
            if monitoring_service:
                self._show_success("✅ Test del sistema completato con successo!")
            else:
                self._show_error("❌ Test del sistema fallito!")
                
        except Exception as ex:
            logger.error(f"Error testing system: {ex}")
            self._show_error(f"❌ Errore nel test del sistema: {ex}")
    
    def _restore_defaults(self, e):
        """Restore default settings"""
        def confirm_restore(e):
            try:
                self.app.page.close(confirm_dialog)
                
                # Reset to defaults
                self.settings = self._get_default_config()
                self._save_settings()
                
                # Update UI components
                self._refresh_ui_components()
                
                self._show_success("✅ Impostazioni ripristinate ai valori predefiniti!")
                
            except Exception as ex:
                logger.error(f"Error restoring defaults: {ex}")
                self._show_error(f"❌ Errore ripristino: {ex}")
        
        def cancel_restore(e):
            self.app.page.close(confirm_dialog)
        
        # Show confirmation dialog
        confirm_dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text("Conferma Ripristino", weight=ft.FontWeight.BOLD),
            content=ft.Text("Sei sicuro di voler ripristinare tutte le impostazioni ai valori predefiniti?\n\nTutte le configurazioni personalizzate verranno perse!"),
            actions=[
                ft.TextButton("Annulla", on_click=cancel_restore),
                ft.ElevatedButton(
                    "Ripristina",
                    on_click=confirm_restore,
                    bgcolor=ft.Colors.ORANGE_600,
                    color=ft.Colors.WHITE
                )
            ],
            actions_alignment=ft.MainAxisAlignment.SPACE_BETWEEN
        )
        
        self.app.page.open(confirm_dialog)
    
    def _refresh_ui_components(self):
        """Refresh UI components with current settings"""
        try:
            if self.enabled_switch.current:
                self.enabled_switch.current.value = self.settings.get('enabled', False)
                self.enabled_switch.current.update()
                
            if self.frequency_dropdown.current:
                self.frequency_dropdown.current.value = self.settings.get('frequency', 'weekly')
                self.frequency_dropdown.current.update()
                
            if self.keywords_field.current:
                keywords = self.settings.get('keywords', [])
                self.keywords_field.current.value = ', '.join(keywords) if isinstance(keywords, list) else keywords
                self.keywords_field.current.update()
                
            if self.api_key_field.current:
                self.api_key_field.current.value = self.settings.get('openrouter_api_key', '')
                self.api_key_field.current.update()
                
            if self.model_dropdown.current:
                # Update AutoComplete suggestions and value
                self.model_dropdown.current.suggestions = [
                    ft.AutoCompleteSuggestion(key=model_id, value=display_name)
                    for model_id, display_name in self._get_model_list()
                ]
                # Set the current value in the AutoComplete field
                current_model = self.settings.get('llm_model', '')
                if current_model:
                    # Find the display name for the current model
                    model_list = self._get_model_list()
                    display_name = next((name for mid, name in model_list if mid == current_model), current_model)
                    self.model_dropdown.current.value = display_name
                else:
                    self.model_dropdown.current.value = ''
                self.model_dropdown.current.update()
                
            if self.llm_enabled_switch.current:
                self.llm_enabled_switch.current.value = self.settings.get('llm_enabled', False)
                self.llm_enabled_switch.current.update()
                
            if self.email_enabled_switch.current:
                self.email_enabled_switch.current.value = self.settings.get('email_notifications', True)
                self.email_enabled_switch.current.update()
                
            if self.notification_email_field.current:
                self.notification_email_field.current.value = self.settings.get('notification_email', '')
                self.notification_email_field.current.update()
                
            if self.connection_status.current:
                self.connection_status.current.value = self._get_connection_status()
                self.connection_status.current.color = self._get_connection_color()
                self.connection_status.current.update()
                
        except Exception as e:
            logger.error(f"Error refreshing UI components: {e}")
    
    def _show_websites_manager(self, e):
        """Show websites management dialog (delegated to main incentives view for now)"""
        try:
            # Import and use the existing websites manager from incentives view
            from ui.views.incentives_view import IncentivesView
            
            # We need to re-load settings to ensure the instance has the latest
            current_settings = self._load_settings()
            
            temp_view = IncentivesView(self.app)
            # Pass the most recent settings to the manager
            temp_view._show_websites_manager(current_settings)
            
        except Exception as ex:
            logger.error(f"Error showing websites manager: {ex}")
            self._show_error(f"❌ Errore apertura gestione siti: {ex}")
    
    def _show_models_manager(self, e):
        """DEPRECATED: Models are now managed via the ComboBox"""
        self._show_info("Gestione modelli integrata nel menù a tendina.")
    
    def _show_success(self, message: str):
        """Show success message"""
        self._show_snackbar(message, ft.Colors.GREEN_600)
    
    def _show_error(self, message: str):
        """Show error message"""
        self._show_snackbar(message, ft.Colors.RED_600)
    
    def _show_info(self, message: str):
        """Show info message"""
        self._show_snackbar(message, ft.Colors.BLUE_600)
    
    def _show_snackbar(self, message: str, color: str):
        """Show snackbar notification"""
        try:
            logger.info(f"NOTIFICATION: {message}")
            
            if hasattr(self.app, 'page') and self.app.page:
                snack = ft.SnackBar(
                    content=ft.Text(message, color=ft.Colors.WHITE),
                    bgcolor=color,
                    duration=3000
                )
                self.app.page.overlay.append(snack)
                snack.open = True
                self.app.page.update()
                
        except Exception as e:
            logger.error(f"Error showing snackbar: {e}")
    
    def refresh_data(self):
        """Refresh settings data"""
        try:
            self.settings = self._load_settings()
            self._refresh_ui_components()
            logger.info("Incentives settings data refreshed")
            
        except Exception as e:
            logger.error(f"Error refreshing incentives settings data: {e}") 