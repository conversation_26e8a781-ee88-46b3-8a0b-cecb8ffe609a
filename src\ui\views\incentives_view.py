#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Incentives Management View
Main interface for viewing and managing Italian financial incentives
"""

import flet as ft
from typing import List, Dict, Any, Optional
from datetime import datetime, date, timedelta
from uuid import UUID

from core.utils.logger import get_logger
from core.models.incentive_models import IncentiveItem, IncentiveStatus, IncentivePriority, IncentiveSource

logger = get_logger(__name__)

class IncentivesView:
    """Main view for incentives management"""
    
    def __init__(self, app_instance):
        self.app = app_instance
        self.db = app_instance.db_manager

        # UI state
        self.current_filter = "all"
        self.search_query = ""
        self.selected_items = []
        self.incentives_data = []
        self.date_filter_start = "2025"
        self.date_filter_end = "2025"

        # Performance optimization: lazy initialization
        self.monitoring_service = None
        self._monitoring_service_initialized = False
        self._data_loaded = False

        # Load initial data asynchronously
        self._schedule_async_init()

    def _schedule_async_init(self):
        """Schedule asynchronous initialization to avoid blocking UI"""
        import threading

        def async_init():
            try:
                # Initialize monitoring service in background
                self._init_monitoring_service()

                # Load data in background
                self.refresh_data()

                # Update UI on main thread
                if hasattr(self, 'app') and hasattr(self.app, 'page'):
                    self.app.page.update()

            except Exception as e:
                logger.error(f"Error in async initialization: {e}")

        # Start background thread
        thread = threading.Thread(target=async_init, daemon=True)
        thread.start()

    def _init_monitoring_service(self):
        """Initialize monitoring service (called in background thread)"""
        if self._monitoring_service_initialized:
            return

        try:
            from core.services.incentives_monitoring_service import IncentivesMonitoringService

            # Load incentives configuration from JSON file
            config = self._load_config_inline()
            logger.info(f"Loaded incentives config: llm_enabled={config.get('llm_enabled')}, api_key_present={bool(config.get('openrouter_api_key'))}")

            self.monitoring_service = IncentivesMonitoringService(
                self.db,
                {'incentives': config},
                self.app.email_service if hasattr(self.app, 'email_service') else None
            )
            logger.info(f"Monitoring service initialized: LLM enabled={self.monitoring_service.llm_service.enabled}")
            self._monitoring_service_initialized = True

        except Exception as e:
            logger.error(f"Could not initialize incentives monitoring service: {e}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            self.monitoring_service = None

    def _get_monitoring_service(self):
        """Get monitoring service, initializing if needed"""
        if not self._monitoring_service_initialized:
            self._init_monitoring_service()
        return self.monitoring_service

    def _load_config_inline(self) -> dict:
        """Load incentives configuration from main settings or JSON file"""
        try:
            # Try to load from main settings first
            if hasattr(self.app, 'settings_manager') and self.app.settings_manager:
                settings = self.app.settings_manager.settings
                if 'incentives' in settings:
                    logger.info("Loading inline config from main settings")
                    return settings['incentives']

            # Fallback to separate config file
            import json
            from pathlib import Path

            config_file = Path("data/incentives_config.json")
            logger.info(f"Loading config from: {config_file}")

            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                logger.info(f"Config loaded successfully: {len(config)} keys")
                return config
            else:
                logger.warning(f"Config file not found: {config_file}")
                # Return default configuration
                from core.models.incentive_models import IncentiveSource
                return {
                    'enabled': True,
                    'frequency': 'weekly',
                    'keywords': ['incentivi', 'finanziamenti', 'bandi'],
                    'openrouter_api_key': '',
                    'llm_model': 'gpt-3.5-turbo',
                    'llm_api_url': 'https://openrouter.ai/api/v1',
                    'llm_enabled': False,
                    'email_notifications': False,
                    'notification_email': '',
                    'sources': [IncentiveSource.MIMIT, IncentiveSource.INVITALIA, IncentiveSource.SIMEST],
                    'user_agent': 'Agevolami PM Bot',
                    'request_delay_seconds': 2.0,
                    'max_items_per_session': 50,
                    'min_relevance_score': 0.5,
                    'websites': [
                        {
                            'name': 'MIMIT - Ministero Imprese',
                            'url': 'https://www.mimit.gov.it',
                            'enabled': True,
                            'search_paths': ['/it/incentivi-mise', '/it/notizie-stampa']
                        },
                        {
                            'name': 'Invitalia',
                            'url': 'https://www.invitalia.it',
                            'enabled': True,
                            'search_paths': ['/per-le-imprese', '/news']
                        },
                        {
                            'name': 'SIMEST',
                            'url': 'https://www.simest.it',
                            'enabled': True,
                            'search_paths': ['/per-le-imprese', '/media']
                        }
                    ]
                }
        except Exception as e:
            logger.error(f"Error loading incentives config: {e}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            # Return minimal default configuration
            from core.models.incentive_models import IncentiveSource
            return {
                'enabled': False,
                'llm_enabled': False,
                'openrouter_api_key': '',
                'llm_model': 'gpt-3.5-turbo',
                'sources': [IncentiveSource.MIMIT, IncentiveSource.INVITALIA, IncentiveSource.SIMEST],
                'user_agent': 'Agevolami PM Bot',
                'request_delay_seconds': 2.0,
                'websites': []
            }

    def build(self) -> ft.Container:
        """Build the incentives view with modern, space-efficient design"""
        return ft.Container(
            content=ft.Column([
                # Compact header with integrated actions
                self._create_modern_header(),

                # Unified control bar with filters, search, and stats
                self._create_unified_control_bar(),

                # Main content area
                self._create_main_content_area()
            ], spacing=8),
            padding=ft.padding.symmetric(horizontal=16, vertical=12),
            expand=True,
            bgcolor=ft.Colors.GREY_50
        )
    
    def _create_modern_header(self) -> ft.Container:
        """Create compact modern header with integrated actions"""
        def run_manual_scan(e):
            """Run manual monitoring scan with enhanced visual feedback"""
            try:
                monitoring_service = self._get_monitoring_service()
                if not monitoring_service:
                    self._show_error("Servizio di monitoraggio non disponibile")
                    return

                # Show loading indicator
                self._show_loading("🔍 Avvio scansione dei siti web...")

                # Run monitoring session in background thread to avoid blocking UI
                import threading
                
                def scan_in_background():
                    try:
                        # Update loading message
                        self._show_loading("📡 Scansione siti web in corso...")
                        
                        # Run monitoring session
                        session = monitoring_service.run_monitoring_session()
                        
                        # Close loading feedback
                        self._close_loading_feedback()
                        
                        # Refresh data on main thread
                        self.refresh_data()
                        
                        # Show results with detailed information
                        if session.new_items > 0:
                            self._show_success(f"✅ Scansione completata: {session.new_items} nuovi incentivi trovati!")
                        elif session.total_items > 0:
                            self._show_info(f"ℹ️ Scansione completata: {session.total_items} incentivi controllati, nessuno nuovo")
                        else:
                            self._show_info("ℹ️ Scansione completata: nessun incentivo trovato")
                        
                    except Exception as ex:
                        logger.error(f"Error in background scan: {ex}")
                        self._close_loading_feedback()
                        self._show_error(f"❌ Errore durante la scansione: {ex}")
                
                # Start background scan
                scan_thread = threading.Thread(target=scan_in_background, daemon=True)
                scan_thread.start()

            except Exception as ex:
                logger.error(f"Error in manual scan setup: {ex}")
                self._show_error(f"❌ Errore nell'avvio della scansione: {ex}")

        def export_to_pdf(e):
            """Export current incentives to PDF"""
            try:
                # TODO: Implement PDF export
                self._show_info("Funzione di esportazione PDF in sviluppo")
            except Exception as ex:
                logger.error(f"Error exporting PDF: {ex}")
                self._show_error(f"Errore durante l'esportazione: {ex}")

        def delete_all_incentives(e):
            """Delete all incentive records with confirmation"""
            def confirm_delete(e):
                try:
                    # Show loading feedback
                    self._show_loading("🗑️ Eliminazione in corso...")
                    
                    monitoring_service = self._get_monitoring_service()
                    if monitoring_service:
                        success = monitoring_service.delete_all_incentives()
                        self._close_loading_feedback()
                        
                        if success:
                            self.refresh_data()
                            self._show_success("✅ Tutti gli incentivi sono stati eliminati con successo")
                        else:
                            self._show_error("❌ Errore durante l'eliminazione degli incentivi")
                    else:
                        self._close_loading_feedback()
                        self._show_error("❌ Servizio di monitoraggio non disponibile")
                except Exception as ex:
                    logger.error(f"Error deleting all incentives: {ex}")
                    self._close_loading_feedback()
                    self._show_error(f"❌ Errore durante l'eliminazione: {ex}")
                finally:
                    confirm_dialog.open = False
                    self.app.page.update()

            def cancel_delete(e):
                confirm_dialog.open = False
                self.app.page.update()

            # Create confirmation dialog
            confirm_dialog = ft.AlertDialog(
                modal=True,
                title=ft.Text("⚠️ Conferma Eliminazione", weight=ft.FontWeight.BOLD),
                content=ft.Text(
                    "Sei sicuro di voler eliminare TUTTI gli incentivi dal database?\n\n"
                    "Questa azione non può essere annullata.",
                    size=14
                ),
                actions=[
                    ft.TextButton("Annulla", on_click=cancel_delete),
                    ft.ElevatedButton(
                        "Elimina Tutto",
                        bgcolor=ft.Colors.RED_600,
                        color=ft.Colors.WHITE,
                        on_click=confirm_delete
                    )
                ],
                actions_alignment=ft.MainAxisAlignment.END
            )

            self.app.page.overlay.append(confirm_dialog)
            confirm_dialog.open = True
            self.app.page.update()

        return ft.Container(
            content=ft.Row([
                # Compact title section
                ft.Row([
                    ft.Icon(ft.Icons.TRENDING_UP, size=24, color=ft.Colors.BLUE_600),
                    ft.Text(
                        "Incentivi Finanziari",
                        size=20,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.GREY_800
                    ),
                    ft.Container(
                        content=ft.Text(
                            "2025",
                            size=11,
                            color=ft.Colors.WHITE,
                            weight=ft.FontWeight.BOLD
                        ),
                        bgcolor=ft.Colors.BLUE_600,
                        padding=ft.padding.symmetric(horizontal=6, vertical=2),
                        border_radius=10
                    )
                ], spacing=8),

                ft.Container(expand=True),

                # Compact action buttons with icons only
                ft.Row([
                    ft.IconButton(
                        icon=ft.Icons.SETTINGS_OUTLINED,
                        tooltip="Configurazione",
                        on_click=lambda _: self._show_configuration_dialog(),
                        icon_color=ft.Colors.GREY_600,
                        bgcolor=ft.Colors.TRANSPARENT
                    ),
                    ft.IconButton(
                        icon=ft.Icons.HEALTH_AND_SAFETY_OUTLINED,
                        tooltip="Stato Sistema",
                        on_click=lambda _: self._show_system_health_dialog(),
                        icon_color=ft.Colors.GREY_600,
                        bgcolor=ft.Colors.TRANSPARENT
                    ),
                    ft.IconButton(
                        icon=ft.Icons.DELETE_FOREVER_OUTLINED,
                        tooltip="Elimina Tutto",
                        on_click=delete_all_incentives,
                        icon_color=ft.Colors.RED_500,
                        bgcolor=ft.Colors.TRANSPARENT
                    ),
                    ft.Container(width=4),
                    ft.ElevatedButton(
                        content=ft.Row([
                            ft.Icon(ft.Icons.REFRESH, size=16),
                            ft.Text("Scansione", size=12)
                        ], spacing=4, tight=True),
                        on_click=run_manual_scan,
                        bgcolor=ft.Colors.BLUE_600,
                        color=ft.Colors.WHITE,
                        height=32,
                        style=ft.ButtonStyle(
                            shape=ft.RoundedRectangleBorder(radius=6)
                        )
                    )
                ], spacing=4)
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            padding=ft.padding.symmetric(horizontal=16, vertical=12),
            bgcolor=ft.Colors.WHITE,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.GREY_200),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=2,
                color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
                offset=ft.Offset(0, 1)
            )
        )
    
    def _create_unified_control_bar(self) -> ft.Container:
        """Create unified control bar with filters, search, and compact stats"""
        def on_filter_change(filter_type):
            """Handle filter change"""
            self.current_filter = filter_type
            self._apply_filters()

        def on_search_change(e):
            """Handle search change"""
            self.search_query = e.control.value.lower().strip()
            self._apply_filters()

        def on_date_filter_change(e):
            """Handle date filter change"""
            self.date_filter_start = start_year_field.value or "2025"
            self.date_filter_end = end_year_field.value or "2025"
            self.refresh_data()

        # Compact filter chips
        filter_chips = []
        filters = [
            ("all", "Tutti", ft.Icons.LIST_ALT, ft.Colors.BLUE_600),
            ("new", "Nuovi", ft.Icons.FIBER_NEW, ft.Colors.GREEN_600),
            ("relevant", "Rilevanti", ft.Icons.STAR, ft.Colors.ORANGE_600),
            ("high_priority", "Alta Priorità", ft.Icons.PRIORITY_HIGH, ft.Colors.RED_600)
        ]

        for filter_key, title, icon, color in filters:
            is_active = filter_key == self.current_filter

            chip = ft.Container(
                content=ft.Row([
                    ft.Icon(icon, size=14, color=ft.Colors.WHITE if is_active else color),
                    ft.Text(
                        title,
                        size=11,
                        color=ft.Colors.WHITE if is_active else ft.Colors.GREY_700,
                        weight=ft.FontWeight.W_500
                    )
                ], spacing=4, tight=True),
                bgcolor=color if is_active else ft.Colors.GREY_50,
                padding=ft.padding.symmetric(horizontal=8, vertical=4),
                border_radius=16,
                border=ft.border.all(1, color if is_active else ft.Colors.GREY_300),
                on_click=lambda e, f=filter_key: on_filter_change(f),
                animate=ft.Animation(150, ft.AnimationCurve.EASE_OUT)
            )
            filter_chips.append(chip)

        # Compact date filter controls
        start_year_field = ft.TextField(
            label="Da",
            value=self.date_filter_start,
            width=70,
            height=32,
            on_change=on_date_filter_change,
            border_radius=6,
            bgcolor=ft.Colors.WHITE,
            text_size=12,
            content_padding=ft.padding.symmetric(horizontal=8, vertical=4)
        )

        end_year_field = ft.TextField(
            label="A",
            value=self.date_filter_end,
            width=70,
            height=32,
            on_change=on_date_filter_change,
            border_radius=6,
            bgcolor=ft.Colors.WHITE,
            text_size=12,
            content_padding=ft.padding.symmetric(horizontal=8, vertical=4)
        )

        # Calculate statistics for compact display (optimized)
        try:
            if not self._data_loaded:
                total_count = new_count = high_priority_count = 0
                last_scan = "Caricamento..."
            else:
                total_count = len(self.incentives_data)
                new_count = sum(1 for i in self.incentives_data if i.get('status') == 'new')
                high_priority_count = sum(1 for i in self.incentives_data if i.get('priority') == 'high')

                # Last scan info
                last_scan = "Mai"
                monitoring_service = self._get_monitoring_service()
                if monitoring_service and monitoring_service.last_run:
                    last_scan = monitoring_service.last_run.strftime("%H:%M")
        except:
            total_count = new_count = high_priority_count = 0
            last_scan = "N/A"

        return ft.Container(
            content=ft.Row([
                # Left side: Filters and search
                ft.Row([
                    # Filter chips
                    ft.Row(filter_chips, spacing=6),
                    ft.Container(width=12),
                    # Date filters
                    ft.Row([
                        ft.Icon(ft.Icons.DATE_RANGE, size=16, color=ft.Colors.GREY_500),
                        start_year_field,
                        ft.Text("-", size=12, color=ft.Colors.GREY_500),
                        end_year_field
                    ], spacing=4),
                ], spacing=8),

                ft.Container(expand=True),

                # Center: Search
                ft.Container(
                    content=ft.TextField(
                        hint_text="Cerca incentivi...",
                        width=250,
                        height=32,
                        on_change=on_search_change,
                        border_radius=16,
                        bgcolor=ft.Colors.WHITE,
                        border_color=ft.Colors.GREY_300,
                        text_size=12,
                        prefix_icon=ft.Icons.SEARCH,
                        content_padding=ft.padding.symmetric(horizontal=12, vertical=4)
                    )
                ),

                ft.Container(expand=True),

                # Right side: Compact stats
                ft.Row([
                    ft.Container(
                        content=ft.Row([
                            ft.Text(str(total_count), size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_600),
                            ft.Text("Tot", size=10, color=ft.Colors.GREY_600)
                        ], spacing=2, tight=True),
                        padding=ft.padding.symmetric(horizontal=6, vertical=2),
                        bgcolor=ft.Colors.BLUE_50,
                        border_radius=8
                    ),
                    ft.Container(
                        content=ft.Row([
                            ft.Text(str(new_count), size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN_600),
                            ft.Text("Nuovi", size=10, color=ft.Colors.GREY_600)
                        ], spacing=2, tight=True),
                        padding=ft.padding.symmetric(horizontal=6, vertical=2),
                        bgcolor=ft.Colors.GREEN_50,
                        border_radius=8
                    ),
                    ft.Container(
                        content=ft.Row([
                            ft.Text(str(high_priority_count), size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.RED_600),
                            ft.Text("Priorità", size=10, color=ft.Colors.GREY_600)
                        ], spacing=2, tight=True),
                        padding=ft.padding.symmetric(horizontal=6, vertical=2),
                        bgcolor=ft.Colors.RED_50,
                        border_radius=8
                    ),
                    ft.Container(
                        content=ft.Row([
                            ft.Icon(ft.Icons.SCHEDULE, size=12, color=ft.Colors.PURPLE_600),
                            ft.Text(last_scan, size=10, color=ft.Colors.PURPLE_600, weight=ft.FontWeight.W_500)
                        ], spacing=2, tight=True),
                        padding=ft.padding.symmetric(horizontal=6, vertical=2),
                        bgcolor=ft.Colors.PURPLE_50,
                        border_radius=8
                    )
                ], spacing=6)
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            padding=ft.padding.symmetric(horizontal=16, vertical=10),
            bgcolor=ft.Colors.WHITE,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.GREY_200),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=2,
                color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
                offset=ft.Offset(0, 1)
            )
        )
    
    def _create_main_content_area(self) -> ft.Container:
        """Create the main content area with incentives list"""
        return ft.Container(
            content=self._create_incentives_list(),
            expand=True
        )
    
    def _create_incentives_list(self) -> ft.Container:
        """Create the modern incentives list with improved layout"""
        # Create container that will hold the incentive cards
        self.incentives_list_container = ft.Container(
            content=ft.Column(
                [],
                spacing=8,
                scroll=ft.ScrollMode.AUTO
            ),
            expand=True,
            bgcolor=ft.Colors.TRANSPARENT
        )

        # Initialize with current data
        self._update_incentives_list()

        return self.incentives_list_container

    def _update_incentives_list(self):
        """Update the incentives list with current filtered data (optimized)"""
        if not hasattr(self, 'incentives_list_container'):
            return

        data_to_show = getattr(self, 'filtered_incentives_data', self.incentives_data)

        if not data_to_show:
            self.incentives_list_container.content = self._create_empty_state().content
        else:
            # Performance optimization: limit initial render to first 50 items
            max_initial_items = 50
            items_to_render = data_to_show[:max_initial_items]

            # Create incentive cards (only for visible items)
            incentive_cards = []
            for item_data in items_to_render:
                card = self._create_incentive_card(item_data)
                incentive_cards.append(card)

            # Add "Load More" button if there are more items
            if len(data_to_show) > max_initial_items:
                load_more_btn = ft.Container(
                    content=ft.ElevatedButton(
                        content=ft.Row([
                            ft.Icon(ft.Icons.EXPAND_MORE, size=16),
                            ft.Text(f"Carica altri {min(50, len(data_to_show) - max_initial_items)} incentivi", size=12)
                        ], spacing=4, tight=True),
                        on_click=lambda _: self._load_more_items(),
                        bgcolor=ft.Colors.BLUE_50,
                        color=ft.Colors.BLUE_700,
                        height=36
                    ),
                    alignment=ft.alignment.center,
                    padding=ft.padding.symmetric(vertical=16)
                )
                incentive_cards.append(load_more_btn)

            self.incentives_list_container.content = ft.Column(
                incentive_cards,
                spacing=8,
                scroll=ft.ScrollMode.AUTO
            )

        # Update the page if available
        if hasattr(self, 'app') and hasattr(self.app, 'page'):
            self.app.page.update()

    def _load_more_items(self):
        """Load more items when requested"""
        try:
            data_to_show = getattr(self, 'filtered_incentives_data', self.incentives_data)
            current_items = len(self.incentives_list_container.content.controls) - 1  # Exclude load more button

            # Load next batch
            next_batch_start = current_items
            next_batch_end = min(current_items + 50, len(data_to_show))

            # Create cards for new items
            new_cards = []
            for i in range(next_batch_start, next_batch_end):
                card = self._create_incentive_card(data_to_show[i])
                new_cards.append(card)

            # Remove old load more button
            self.incentives_list_container.content.controls.pop()

            # Add new cards
            self.incentives_list_container.content.controls.extend(new_cards)

            # Add new load more button if needed
            if next_batch_end < len(data_to_show):
                load_more_btn = ft.Container(
                    content=ft.ElevatedButton(
                        content=ft.Row([
                            ft.Icon(ft.Icons.EXPAND_MORE, size=16),
                            ft.Text(f"Carica altri {min(50, len(data_to_show) - next_batch_end)} incentivi", size=12)
                        ], spacing=4, tight=True),
                        on_click=lambda _: self._load_more_items(),
                        bgcolor=ft.Colors.BLUE_50,
                        color=ft.Colors.BLUE_700,
                        height=36
                    ),
                    alignment=ft.alignment.center,
                    padding=ft.padding.symmetric(vertical=16)
                )
                self.incentives_list_container.content.controls.append(load_more_btn)

            self.app.page.update()

        except Exception as e:
            logger.error(f"Error loading more items: {e}")
    
    def _create_empty_state(self) -> ft.Container:
        """Create modern empty state when no incentives are found"""
        return ft.Container(
            content=ft.Column([
                ft.Container(
                    content=ft.Icon(ft.Icons.SEARCH_OFF, size=48, color=ft.Colors.GREY_400),
                    padding=ft.padding.all(16),
                    bgcolor=ft.Colors.GREY_50,
                    border_radius=24
                ),
                ft.Text(
                    "Nessun incentivo trovato",
                    size=18,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.GREY_700
                ),
                ft.Text(
                    "Avvia una scansione manuale o configura il monitoraggio automatico",
                    size=13,
                    color=ft.Colors.GREY_500,
                    text_align=ft.TextAlign.CENTER
                ),
                ft.Container(height=16),
                ft.ElevatedButton(
                    content=ft.Row([
                        ft.Icon(ft.Icons.PLAY_ARROW, size=16),
                        ft.Text("Avvia Prima Scansione", size=13)
                    ], spacing=4, tight=True),
                    bgcolor=ft.Colors.BLUE_600,
                    color=ft.Colors.WHITE,
                    height=36,
                    style=ft.ButtonStyle(
                        shape=ft.RoundedRectangleBorder(radius=8)
                    ),
                    on_click=lambda _: self._run_first_scan()
                )
            ],
            spacing=12,
            horizontal_alignment=ft.CrossAxisAlignment.CENTER
            ),
            padding=ft.padding.all(32),
            bgcolor=ft.Colors.WHITE,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.GREY_200),
            alignment=ft.alignment.center,
            expand=True,
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=4,
                color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
                offset=ft.Offset(0, 2)
            )
        )
    
    def _create_incentive_card(self, item_data: Dict[str, Any]) -> ft.Container:
        """Create a modern, compact card for a single incentive"""
        # Priority color mapping
        priority_colors = {
            'high': ft.Colors.RED_500,
            'medium': ft.Colors.ORANGE_500,
            'low': ft.Colors.GREEN_500
        }

        priority = item_data.get('priority', 'medium')
        priority_color = priority_colors.get(priority, ft.Colors.GREY_500)

        # Source icon mapping
        source_icons = {
            'mimit.gov.it': ft.Icons.ACCOUNT_BALANCE,
            'invitalia.it': ft.Icons.BUSINESS,
            'simest.it': ft.Icons.PUBLIC,
            'regione.campania.it': ft.Icons.LOCATION_CITY
        }

        source = item_data.get('source', 'other')
        source_icon = source_icons.get(source, ft.Icons.LANGUAGE)

        # Truncate title and description for compact display
        title = item_data.get('title', 'Titolo non disponibile')
        if len(title) > 80:
            title = title[:77] + "..."

        description = item_data.get('description', 'Descrizione non disponibile')
        if len(description) > 120:
            description = description[:117] + "..."

        return ft.Container(
            content=ft.Column([
                # Main content row
                ft.Row([
                    # Left side: Content
                    ft.Column([
                        # Title with priority indicator
                        ft.Row([
                            ft.Container(
                                width=4,
                                height=20,
                                bgcolor=priority_color,
                                border_radius=2
                            ),
                            ft.Text(
                                title,
                                size=14,
                                weight=ft.FontWeight.W_600,
                                color=ft.Colors.GREY_800,
                                expand=True
                            )
                        ], spacing=8),

                        # Description
                        ft.Text(
                            description,
                            size=12,
                            color=ft.Colors.GREY_600,
                            max_lines=2
                        ),

                        # LLM Summary if available (compact)
                        ft.Container(
                            content=ft.Text(
                                item_data.get('llm_summary', '').split('\n')[0][:80] + "..." if item_data.get('llm_summary') and len(item_data.get('llm_summary', '').split('\n')[0]) > 80 else item_data.get('llm_summary', '').split('\n')[0],
                                size=11,
                                color=ft.Colors.BLUE_600,
                                italic=True,
                                weight=ft.FontWeight.W_400
                            ),
                            visible=bool(item_data.get('llm_summary'))
                        )
                    ], expand=True, spacing=6),

                    # Right side: Actions
                    ft.Column([
                        ft.Row([
                            ft.IconButton(
                                icon=ft.Icons.INFO_OUTLINE,
                                icon_size=18,
                                tooltip="Dettagli",
                                icon_color=ft.Colors.BLUE_600,
                                on_click=lambda e, data=item_data: self._show_incentive_details(data)
                            ),
                            ft.IconButton(
                                icon=ft.Icons.OPEN_IN_NEW,
                                icon_size=18,
                                tooltip="Apri fonte",
                                icon_color=ft.Colors.GREY_600,
                                on_click=lambda e, url=item_data.get('source_url'): self._open_url(url) if url else None
                            ),
                            ft.IconButton(
                                icon=ft.Icons.DELETE_OUTLINE,
                                icon_size=18,
                                tooltip="Elimina",
                                icon_color=ft.Colors.RED_500,
                                on_click=lambda e, data=item_data: self._delete_incentive_with_confirmation(data)
                            )
                        ], spacing=0, tight=True)
                    ], horizontal_alignment=ft.CrossAxisAlignment.END)
                ]),

                # Footer with metadata
                ft.Row([
                    ft.Row([
                        ft.Icon(source_icon, size=14, color=ft.Colors.GREY_500),
                        ft.Text(source, size=11, color=ft.Colors.GREY_500)
                    ], spacing=4),

                    ft.Container(expand=True),

                    ft.Row([
                        ft.Container(
                            content=ft.Text(
                                priority.upper(),
                                size=9,
                                color=ft.Colors.WHITE,
                                weight=ft.FontWeight.BOLD
                            ),
                            bgcolor=priority_color,
                            padding=ft.padding.symmetric(horizontal=6, vertical=2),
                            border_radius=8
                        ),
                        ft.Text(
                            item_data.get('found_date', 'N/A')[:10],
                            size=11,
                            color=ft.Colors.GREY_500
                        )
                    ], spacing=8)
                ])
            ], spacing=8),
            padding=ft.padding.all(12),
            bgcolor=ft.Colors.WHITE,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.GREY_200),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=2,
                color=ft.Colors.with_opacity(0.08, ft.Colors.BLACK),
                offset=ft.Offset(0, 1)
            ),
            animate=ft.Animation(150, ft.AnimationCurve.EASE_OUT)
        )
    
    def refresh_data(self):
        """Refresh incentives data from database (optimized)"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()

                # Build date filter based on selected range
                start_date = f"{self.date_filter_start}-01-01"
                end_date = f"{self.date_filter_end}-12-31"

                # Optimized query: only select needed columns and limit initial results
                cursor.execute("""
                    SELECT id, title, description, source, source_url, llm_summary,
                           priority, llm_relevance_score, found_date, status
                    FROM incentives
                    WHERE (
                        (found_date >= ? AND found_date <= ?) OR
                        (publication_date >= ? AND publication_date <= ?) OR
                        (deadline_date >= ? AND deadline_date <= ?)
                    )
                    ORDER BY found_date DESC, llm_relevance_score DESC
                    LIMIT 1000
                """, (start_date, end_date, start_date, end_date, start_date, end_date))
                rows = cursor.fetchall()

                self.incentives_data = [dict(row) for row in rows]
                self._data_loaded = True
                self._apply_filters()

                logger.info(f"Loaded {len(self.incentives_data)} incentives for date range {self.date_filter_start}-{self.date_filter_end}")

        except Exception as e:
            logger.error(f"Error refreshing incentives data: {e}")
            self.incentives_data = []
            self._data_loaded = True

    def _apply_filters(self):
        """Apply current filters to the data"""
        filtered_data = self.incentives_data.copy()

        # Apply status filter
        if self.current_filter != "all":
            if self.current_filter == "new":
                filtered_data = [item for item in filtered_data if item.get('status') == 'new']
            elif self.current_filter == "relevant":
                filtered_data = [item for item in filtered_data if item.get('status') == 'relevant']
            elif self.current_filter == "high_priority":
                filtered_data = [item for item in filtered_data if item.get('priority') == 'high']

        # Apply search filter
        if self.search_query:
            filtered_data = [
                item for item in filtered_data
                if (self.search_query in item.get('title', '').lower() or
                    self.search_query in item.get('description', '').lower() or
                    self.search_query in item.get('llm_summary', '').lower())
            ]

        # Update the UI with filtered data
        self.filtered_incentives_data = filtered_data
        self._update_incentives_list()
    
    def _open_url(self, url: str):
        """Open URL in browser"""
        try:
            import webbrowser
            webbrowser.open(url)
        except Exception as e:
            logger.error(f"Error opening URL: {e}")
    
    def _show_loading(self, message: str):
        """Show loading message with progress indicator"""
        logger.info(f"LOADING: {message}")
        
        try:
            # Create loading snack bar
            loading_snack = ft.SnackBar(
                content=ft.Row([
                    ft.ProgressRing(width=20, height=20, stroke_width=3),
                    ft.Text(message, size=14, color=ft.Colors.WHITE)
                ], spacing=12),
                bgcolor=ft.Colors.BLUE_600,
                duration=5000,  # 5 seconds
                show_close_icon=True
            )
            
            if hasattr(self, 'app') and hasattr(self.app, 'page'):
                self.app.page.overlay.append(loading_snack)
                loading_snack.open = True
                self.app.page.update()
                
        except Exception as e:
            logger.error(f"Error showing loading feedback: {e}")
    
    def _show_success(self, message: str):
        """Show success message with visual feedback"""
        logger.info(f"SUCCESS: {message}")
        
        try:
            # Create success snack bar
            success_snack = ft.SnackBar(
                content=ft.Row([
                    ft.Icon(ft.Icons.CHECK_CIRCLE, color=ft.Colors.WHITE, size=20),
                    ft.Text(message, size=14, color=ft.Colors.WHITE)
                ], spacing=8),
                bgcolor=ft.Colors.GREEN_600,
                duration=4000,  # 4 seconds
                show_close_icon=True
            )
            
            if hasattr(self, 'app') and hasattr(self.app, 'page'):
                self.app.page.overlay.append(success_snack)
                success_snack.open = True
                self.app.page.update()
                
        except Exception as e:
            logger.error(f"Error showing success feedback: {e}")
    
    def _show_error(self, message: str):
        """Show error message with visual feedback"""
        logger.error(f"ERROR: {message}")
        
        try:
            # Create error snack bar
            error_snack = ft.SnackBar(
                content=ft.Row([
                    ft.Icon(ft.Icons.ERROR, color=ft.Colors.WHITE, size=20),
                    ft.Text(message, size=14, color=ft.Colors.WHITE)
                ], spacing=8),
                bgcolor=ft.Colors.RED_600,
                duration=6000,  # 6 seconds for errors
                show_close_icon=True
            )
            
            if hasattr(self, 'app') and hasattr(self.app, 'page'):
                self.app.page.overlay.append(error_snack)
                error_snack.open = True
                self.app.page.update()
                
        except Exception as e:
            logger.error(f"Error showing error feedback: {e}")

    def _show_info(self, message: str):
        """Show info message with visual feedback"""
        logger.info(f"INFO: {message}")
        
        try:
            # Create info snack bar
            info_snack = ft.SnackBar(
                content=ft.Row([
                    ft.Icon(ft.Icons.INFO, color=ft.Colors.WHITE, size=20),
                    ft.Text(message, size=14, color=ft.Colors.WHITE)
                ], spacing=8),
                bgcolor=ft.Colors.BLUE_500,
                duration=4000,  # 4 seconds
                show_close_icon=True
            )
            
            if hasattr(self, 'app') and hasattr(self.app, 'page'):
                self.app.page.overlay.append(info_snack)
                info_snack.open = True
                self.app.page.update()
                
        except Exception as e:
            logger.error(f"Error showing info feedback: {e}")

    def _close_loading_feedback(self):
        """Close any active loading feedback"""
        try:
            if hasattr(self, 'app') and hasattr(self.app, 'page'):
                # Close any open snack bars by clearing overlays with SnackBar type
                overlays_to_remove = []
                for overlay in self.app.page.overlay:
                    if isinstance(overlay, ft.SnackBar) and overlay.open:
                        overlay.open = False
                        overlays_to_remove.append(overlay)
                
                # Remove closed snack bars from overlay
                for overlay in overlays_to_remove:
                    if overlay in self.app.page.overlay:
                        self.app.page.overlay.remove(overlay)
                
                self.app.page.update()
                
        except Exception as e:
            logger.error(f"Error closing loading feedback: {e}")

    def _run_first_scan(self):
        """Run first scan with enhanced visual feedback"""
        try:
            monitoring_service = self._get_monitoring_service()
            if not monitoring_service:
                self._show_error("❌ Servizio di monitoraggio non disponibile")
                return

            # Show loading indicator
            self._show_loading("🚀 Avvio prima scansione del sistema...")

            # Run monitoring session in background thread
            import threading
            
            def first_scan_background():
                try:
                    # Update loading message
                    self._show_loading("📡 Prima scansione in corso - Configurazione sistema...")
                    
                    # Run monitoring session
                    session = monitoring_service.run_monitoring_session()
                    
                    # Close loading feedback
                    self._close_loading_feedback()
                    
                    # Refresh data
                    self.refresh_data()
                    
                    # Show results with welcome message
                    if session.new_items > 0:
                        self._show_success(f"🎉 Prima scansione completata! Trovati {session.new_items} incentivi. Benvenuto in Agevolami PM!")
                    else:
                        self._show_info("ℹ️ Prima scansione completata. Sistema configurato correttamente. Puoi ora utilizzare il monitoraggio automatico.")
                    
                except Exception as ex:
                    logger.error(f"Error in first scan: {ex}")
                    self._close_loading_feedback()
                    self._show_error(f"❌ Errore nella prima scansione: {ex}")
            
            # Start background scan
            scan_thread = threading.Thread(target=first_scan_background, daemon=True)
            scan_thread.start()

        except Exception as ex:
            logger.error(f"Error in first scan setup: {ex}")
            self._show_error(f"❌ Errore nell'avvio della prima scansione: {ex}")

    def _delete_incentive_with_confirmation(self, item_data: Dict[str, Any]):
        """Delete incentive with confirmation dialog"""
        def confirm_delete(e):
            try:
                incentive_id = item_data.get('id')
                if not incentive_id:
                    self._show_error("❌ ID incentivo non trovato")
                    return

                # Show loading feedback
                self._show_loading("🗑️ Eliminazione incentivo in corso...")

                # Delete from database
                with self.db.get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute("DELETE FROM incentives WHERE id = ?", (incentive_id,))
                    conn.commit()

                # Close loading feedback
                self._close_loading_feedback()

                # Refresh data
                self.refresh_data()
                self._show_success("✅ Incentivo eliminato con successo")

            except Exception as ex:
                logger.error(f"Error deleting incentive: {ex}")
                self._close_loading_feedback()
                self._show_error(f"❌ Errore durante l'eliminazione: {ex}")
            finally:
                confirm_dialog.open = False
                self.app.page.update()

        def cancel_delete(e):
            confirm_dialog.open = False
            self.app.page.update()

        # Create confirmation dialog
        confirm_dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text("⚠️ Conferma Eliminazione", weight=ft.FontWeight.BOLD),
            content=ft.Text(
                f"Sei sicuro di voler eliminare l'incentivo:\n\n"
                f"'{item_data.get('title', 'Titolo non disponibile')}'?\n\n"
                f"Questa azione non può essere annullata.",
                size=14
            ),
            actions=[
                ft.TextButton("Annulla", on_click=cancel_delete),
                ft.ElevatedButton(
                    "Elimina",
                    bgcolor=ft.Colors.RED_600,
                    color=ft.Colors.WHITE,
                    on_click=confirm_delete
                )
            ],
            actions_alignment=ft.MainAxisAlignment.END
        )

        self.app.page.overlay.append(confirm_dialog)
        confirm_dialog.open = True
        self.app.page.update()

    def _delete_incentive_from_details(self, item_data: Dict[str, Any], details_dialog):
        """Delete incentive from details dialog"""
        def confirm_delete(e):
            try:
                incentive_id = item_data.get('id')
                if not incentive_id:
                    self._show_error("ID incentivo non trovato")
                    return

                # Delete from database
                with self.db.get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute("DELETE FROM incentives WHERE id = ?", (incentive_id,))
                    conn.commit()

                # Close both dialogs
                details_dialog.open = False
                confirm_dialog.open = False

                # Refresh data
                self.refresh_data()
                self._show_success("✅ Incentivo eliminato con successo")

            except Exception as ex:
                logger.error(f"Error deleting incentive: {ex}")
                self._show_error(f"Errore durante l'eliminazione: {ex}")
            finally:
                confirm_dialog.open = False
                self.app.page.update()

        def cancel_delete(e):
            confirm_dialog.open = False
            self.app.page.update()

        # Create confirmation dialog
        confirm_dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text("⚠️ Conferma Eliminazione", weight=ft.FontWeight.BOLD),
            content=ft.Text(
                f"Sei sicuro di voler eliminare l'incentivo:\n\n"
                f"'{item_data.get('title', 'Titolo non disponibile')}'?\n\n"
                f"Questa azione non può essere annullata.",
                size=14
            ),
            actions=[
                ft.TextButton("Annulla", on_click=cancel_delete),
                ft.ElevatedButton(
                    "Elimina",
                    bgcolor=ft.Colors.RED_600,
                    color=ft.Colors.WHITE,
                    on_click=confirm_delete
                )
            ],
            actions_alignment=ft.MainAxisAlignment.END
        )

        self.app.page.overlay.append(confirm_dialog)
        confirm_dialog.open = True
        self.app.page.update()
    
    def _show_incentive_details(self, item_data: Dict[str, Any]):
        """Show modern detailed view of an incentive"""
        try:
            title = item_data.get('title', 'Titolo non disponibile')
            description = item_data.get('description', 'Descrizione non disponibile')
            source = item_data.get('source', 'Fonte sconosciuta')
            source_url = item_data.get('source_url', '')
            llm_summary = item_data.get('llm_summary', '')
            priority = item_data.get('priority', 'medium')
            relevance_score = item_data.get('llm_relevance_score', 0.0)
            found_date = item_data.get('found_date', 'N/A')

            # Priority color mapping
            priority_colors = {
                'high': ft.Colors.RED_500,
                'medium': ft.Colors.ORANGE_500,
                'low': ft.Colors.GREEN_500
            }
            priority_color = priority_colors.get(priority, ft.Colors.GREY_500)

            # Parse LLM summary for structured information
            summary_lines = llm_summary.split('\n') if llm_summary else []
            main_summary = summary_lines[0] if summary_lines else description

            # Extract structured info
            structured_info = []
            for line in summary_lines[1:]:
                if line.strip():
                    structured_info.append(line.strip())

            def close_dialog(e):
                dialog.open = False
                self.app.page.update()

            def open_source(e):
                if source_url:
                    self._open_url(source_url)

            def reanalyze_incentive(e):
                """Re-analyze this incentive with LLM"""
                try:
                    monitoring_service = self._get_monitoring_service()
                    if not monitoring_service:
                        self._show_error("Servizio di monitoraggio non disponibile")
                        return

                    incentive_id = item_data.get('id')
                    if not incentive_id:
                        self._show_error("ID incentivo non trovato")
                        return

                    # Show loading
                    self._show_loading("Rianalisi in corso...")

                    # Re-analyze the incentive
                    success = monitoring_service.reanalyze_incentive(incentive_id)

                    if success:
                        # Close current dialog
                        dialog.open = False
                        self.app.page.update()

                        # Refresh data and show updated details
                        self.refresh_data()
                        self._show_success("✅ Incentivo rianalizzato con successo!")

                    else:
                        self._show_error("❌ Errore durante la rianalisi")

                except Exception as ex:
                    logger.error(f"Error re-analyzing incentive: {ex}")
                    self._show_error(f"Errore durante la rianalisi: {ex}")

            # Create content sections list
            content_sections = []

            # Title section with priority indicator
            content_sections.append(
                ft.Container(
                    content=ft.Row([
                        ft.Container(
                            width=4,
                            height=24,
                            bgcolor=priority_color,
                            border_radius=2
                        ),
                        ft.Text(
                            title,
                            size=16,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.GREY_800,
                            expand=True,
                            selectable=True
                        )
                    ], spacing=12),
                    padding=ft.padding.all(12),
                    margin=ft.margin.only(bottom=8)
                )
            )

            # Metadata chips section
            content_sections.append(
                ft.Container(
                    content=ft.Row([
                        ft.Container(
                            content=ft.Row([
                                ft.Icon(ft.Icons.LANGUAGE, size=14, color=ft.Colors.BLUE_600),
                                ft.Text(source, size=12, color=ft.Colors.BLUE_600, weight=ft.FontWeight.W_500)
                            ], spacing=4, tight=True),
                            bgcolor=ft.Colors.BLUE_50,
                            padding=ft.padding.symmetric(horizontal=8, vertical=4),
                            border_radius=12,
                            border=ft.border.all(1, ft.Colors.BLUE_200)
                        ),
                        ft.Container(
                            content=ft.Row([
                                ft.Icon(ft.Icons.PRIORITY_HIGH, size=14, color=priority_color),
                                ft.Text(priority.upper(), size=12, color=priority_color, weight=ft.FontWeight.W_500)
                            ], spacing=4, tight=True),
                            bgcolor=ft.Colors.with_opacity(0.1, priority_color),
                            padding=ft.padding.symmetric(horizontal=8, vertical=4),
                            border_radius=12,
                            border=ft.border.all(1, ft.Colors.with_opacity(0.3, priority_color))
                        ),
                        ft.Container(
                            content=ft.Row([
                                ft.Icon(ft.Icons.STAR, size=14, color=ft.Colors.AMBER_600),
                                ft.Text(f"{relevance_score:.1f}", size=12, color=ft.Colors.AMBER_600, weight=ft.FontWeight.W_500)
                            ], spacing=4, tight=True),
                            bgcolor=ft.Colors.AMBER_50,
                            padding=ft.padding.symmetric(horizontal=8, vertical=4),
                            border_radius=12,
                            border=ft.border.all(1, ft.Colors.AMBER_200)
                        ),
                        ft.Container(
                            content=ft.Row([
                                ft.Icon(ft.Icons.SCHEDULE, size=14, color=ft.Colors.GREY_600),
                                ft.Text(found_date[:10], size=12, color=ft.Colors.GREY_600, weight=ft.FontWeight.W_500)
                            ], spacing=4, tight=True),
                            bgcolor=ft.Colors.GREY_50,
                            padding=ft.padding.symmetric(horizontal=8, vertical=4),
                            border_radius=12,
                            border=ft.border.all(1, ft.Colors.GREY_200)
                        )
                    ], spacing=8, wrap=True),
                    padding=ft.padding.all(12),
                    margin=ft.margin.only(bottom=8)
                )
            )

            # Main summary section
            if main_summary:
                content_sections.append(
                    ft.Container(
                        content=ft.Column([
                            ft.Row([
                                ft.Icon(ft.Icons.SUMMARIZE, size=16, color=ft.Colors.BLUE_600),
                                ft.Text("Riassunto", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_600)
                            ], spacing=6),
                            ft.Text(
                                main_summary,
                                size=13,
                                color=ft.Colors.GREY_700,
                                selectable=True
                            )
                        ], spacing=8),
                        padding=ft.padding.all(12),
                        bgcolor=ft.Colors.BLUE_50,
                        border_radius=8,
                        border=ft.border.all(1, ft.Colors.BLUE_200),
                        margin=ft.margin.only(bottom=8)
                    )
                )

            # Structured information section
            if structured_info:
                content_sections.append(
                    ft.Container(
                        content=ft.Column([
                            ft.Row([
                                ft.Icon(ft.Icons.LIST_ALT, size=16, color=ft.Colors.GREEN_600),
                                ft.Text("Informazioni Dettagliate", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN_600)
                            ], spacing=6),
                            ft.Column([
                                ft.Row([
                                    ft.Container(
                                        width=4,
                                        height=16,
                                        bgcolor=ft.Colors.GREEN_400,
                                        border_radius=2
                                    ),
                                    ft.Text(
                                        info,
                                        size=12,
                                        color=ft.Colors.GREY_700,
                                        expand=True,
                                        selectable=True
                                    )
                                ], spacing=8)
                                for info in structured_info
                            ], spacing=6)
                        ], spacing=8),
                        padding=ft.padding.all(12),
                        bgcolor=ft.Colors.GREEN_50,
                        border_radius=8,
                        border=ft.border.all(1, ft.Colors.GREEN_200),
                        margin=ft.margin.only(bottom=8)
                    )
                )

            # Original description section (if different)
            if description != main_summary and len(description) > 50:
                content_sections.append(
                    ft.Container(
                        content=ft.Column([
                            ft.Row([
                                ft.Icon(ft.Icons.DESCRIPTION, size=16, color=ft.Colors.ORANGE_600),
                                ft.Text("Descrizione Originale", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.ORANGE_600)
                            ], spacing=6),
                            ft.Text(
                                description,
                                size=12,
                                color=ft.Colors.GREY_600,
                                selectable=True
                            )
                        ], spacing=8),
                        padding=ft.padding.all(12),
                        bgcolor=ft.Colors.ORANGE_50,
                        border_radius=8,
                        border=ft.border.all(1, ft.Colors.ORANGE_200),
                        margin=ft.margin.only(bottom=8)
                    )
                )

            # Source URL section
            if source_url:
                content_sections.append(
                    ft.Container(
                        content=ft.Column([
                            ft.Row([
                                ft.Icon(ft.Icons.LINK, size=16, color=ft.Colors.PURPLE_600),
                                ft.Text("Fonte", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.PURPLE_600)
                            ], spacing=6),
                            ft.Container(
                                content=ft.Text(
                                    source_url,
                                    size=11,
                                    color=ft.Colors.BLUE_600,
                                    selectable=True
                                ),
                                padding=ft.padding.all(8),
                                bgcolor=ft.Colors.WHITE,
                                border_radius=6,
                                border=ft.border.all(1, ft.Colors.GREY_300)
                            )
                        ], spacing=8),
                        padding=ft.padding.all(12),
                        bgcolor=ft.Colors.PURPLE_50,
                        border_radius=8,
                        border=ft.border.all(1, ft.Colors.PURPLE_200),
                        margin=ft.margin.only(bottom=8)
                    )
                )

            # Create simplified modern dialog
            dialog = ft.AlertDialog(
                title=ft.Row([
                    ft.Icon(ft.Icons.INFO_OUTLINE, size=20, color=ft.Colors.BLUE_600),
                    ft.Text("Dettagli Incentivo", size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_800)
                ], spacing=8),
                content=ft.Container(
                    content=ft.Column(
                        content_sections,
                        spacing=8,
                        scroll=ft.ScrollMode.AUTO
                    ),
                    width=700,
                    height=500
                ),
                actions=[
                    ft.TextButton(
                        "Chiudi",
                        on_click=close_dialog,
                        style=ft.ButtonStyle(
                            color=ft.Colors.GREY_600
                        )
                    ),
                    ft.OutlinedButton(
                        content=ft.Row([
                            ft.Icon(ft.Icons.DELETE_OUTLINE, size=16, color=ft.Colors.RED_600),
                            ft.Text("Elimina", size=12)
                        ], spacing=4, tight=True),
                        on_click=lambda e: self._delete_incentive_from_details(item_data, dialog),
                        tooltip="Elimina questo incentivo",
                        style=ft.ButtonStyle(
                            color=ft.Colors.RED_600,
                            side=ft.BorderSide(1, ft.Colors.RED_300)
                        )
                    ),
                    ft.OutlinedButton(
                        content=ft.Row([
                            ft.Icon(ft.Icons.PSYCHOLOGY, size=16, color=ft.Colors.PURPLE_600),
                            ft.Text("Rianalizza", size=12)
                        ], spacing=4, tight=True),
                        on_click=reanalyze_incentive,
                        tooltip="Re-scrape e rianalizza questo incentivo con LLM",
                        style=ft.ButtonStyle(
                            color=ft.Colors.PURPLE_600,
                            side=ft.BorderSide(1, ft.Colors.PURPLE_300)
                        )
                    ),
                    ft.ElevatedButton(
                        content=ft.Row([
                            ft.Icon(ft.Icons.OPEN_IN_NEW, size=16),
                            ft.Text("Apri Fonte", size=12)
                        ], spacing=4, tight=True),
                        on_click=open_source,
                        disabled=not source_url,
                        bgcolor=ft.Colors.BLUE_600,
                        color=ft.Colors.WHITE,
                        style=ft.ButtonStyle(
                            shape=ft.RoundedRectangleBorder(radius=6)
                        )
                    )
                ],
                actions_alignment=ft.MainAxisAlignment.END
            )

            self.app.page.overlay.append(dialog)
            dialog.open = True
            self.app.page.update()

        except Exception as e:
            logger.error(f"Error showing incentive details: {e}")
            self._show_error(f"Errore nella visualizzazione dei dettagli: {e}")

    def _show_configuration_dialog(self):
        """Show configuration dialog for incentives monitoring"""
        try:
            # Load current configuration
            config_file = self.app.config.data_dir / 'incentives_config.json'
            current_config = self._load_incentives_config()

            # Create form fields
            api_url_field = ft.TextField(
                label="API URL",
                hint_text="https://openrouter.ai/api/v1",
                width=400,
                value=current_config.get('llm_api_url', 'https://openrouter.ai/api/v1')
            )

            api_key_field = ft.TextField(
                label="OpenRouter API Key",
                hint_text="sk-or-...",
                password=True,
                width=400,
                value=current_config.get('openrouter_api_key', '')
            )

            # Get available models (custom + defaults)
            available_models = self._get_available_models(current_config)
            current_model = current_config.get('llm_model', 'gpt-3.5-turbo')

            model_field = ft.TextField(
                label="Modello LLM (personalizzabile)",
                hint_text="gpt-4, claude-3-sonnet, llama-2-70b, ecc.",
                width=300,
                value=current_model,
                suffix=ft.PopupMenuButton(
                    icon=ft.Icons.ARROW_DROP_DOWN,
                    tooltip="Modelli comuni",
                    items=[
                        ft.PopupMenuItem(
                            text=model_name,
                            on_click=lambda e, model=model_id: setattr(model_field, 'value', model) or model_field.update()
                        )
                        for model_id, model_name in available_models.items()
                    ]
                )
            )

            frequency_dropdown = ft.Dropdown(
                label="Frequenza Monitoraggio",
                width=200,
                options=[
                    ft.dropdown.Option("daily", "Giornaliero"),
                    ft.dropdown.Option("weekly", "Settimanale"),
                    ft.dropdown.Option("monthly", "Mensile")
                ],
                value=current_config.get('frequency', 'weekly')
            )

            keywords_field = ft.TextField(
                label="Parole Chiave (separate da virgola)",
                hint_text="incentivi, finanziamenti, bandi, agevolazioni",
                width=400,
                value=', '.join(current_config.get('keywords', []))
            )

            email_field = ft.TextField(
                label="Email per Notifiche",
                hint_text="<EMAIL>",
                width=400,
                value=current_config.get('notification_email', '')
            )

            enabled_switch = ft.Switch(
                label="Monitoraggio Attivo",
                value=current_config.get('enabled', False)
            )

            email_notifications_switch = ft.Switch(
                label="Notifiche Email",
                value=current_config.get('email_notifications', True)
            )

            # Websites list
            websites_list = self._create_websites_list(current_config.get('websites', []))

            def test_llm_connection(e):
                """Test LLM connection with visual feedback"""
                try:
                    # Get current configuration
                    test_config = {
                        'openrouter_api_key': api_key_field.value.strip(),
                        'llm_model': model_field.value.strip(),
                        'llm_enabled': True,
                        'llm_api_url': api_url_field.value.strip()
                    }

                    if not test_config['openrouter_api_key']:
                        self._show_error("❌ API Key non configurata!")
                        return

                    if not test_config['llm_model']:
                        self._show_error("❌ Modello non selezionato!")
                        return

                    # Show loading feedback
                    self._show_loading("🔗 Test connessione LLM in corso...")

                    # Import here to avoid circular imports
                    from core.services.llm_service import LLMService

                    # Test connection
                    llm_service = LLMService(test_config)

                    if llm_service.test_connection():
                        self._close_loading_feedback()
                        self._show_success("✅ Connessione LLM riuscita!")
                    else:
                        self._close_loading_feedback()
                        self._show_error("❌ Test di connessione fallito!")

                except Exception as ex:
                    logger.error(f"Error testing LLM connection: {ex}")
                    self._close_loading_feedback()
                    self._show_error(f"❌ Errore nel test: {ex}")

            def save_config(e):
                """Save configuration with visual feedback"""
                try:
                    # Show loading feedback
                    self._show_loading("💾 Salvataggio configurazione...")

                    new_config = {
                        'enabled': enabled_switch.value,
                        'frequency': frequency_dropdown.value,
                        'keywords': [k.strip() for k in keywords_field.value.split(',') if k.strip()],
                        'openrouter_api_key': api_key_field.value,
                        'llm_model': model_field.value.strip(),
                        'llm_api_url': api_url_field.value.strip(),
                        'llm_enabled': bool(api_key_field.value),
                        'email_notifications': email_notifications_switch.value,
                        'notification_email': email_field.value,
                        'websites': current_config.get('websites', []),
                        'custom_models': current_config.get('custom_models', {})
                    }

                    self._save_incentives_config(new_config)
                    self._close_loading_feedback()
                    
                    dialog.open = False
                    self.app.page.update()
                    self._show_success("✅ Configurazione salvata con successo!")

                except Exception as ex:
                    logger.error(f"Error saving config: {ex}")
                    self._close_loading_feedback()
                    self._show_error(f"❌ Errore nel salvataggio: {ex}")

            def close_dialog(e):
                """Close dialog"""
                dialog.open = False
                self.app.page.update()

            # Create dialog
            dialog = ft.AlertDialog(
                title=ft.Text("🎯 Configurazione Monitoraggio Incentivi", weight=ft.FontWeight.BOLD),
                content=ft.Container(
                    content=ft.Column([
                        ft.Text("🤖 Configurazione LLM", size=16, weight=ft.FontWeight.BOLD),
                        api_url_field,
                        api_key_field,
                        ft.Row([
                            model_field,
                            ft.ElevatedButton(
                                text="Gestisci Modelli",
                                icon=ft.Icons.EDIT,
                                on_click=lambda _: self._show_models_manager(current_config)
                            )
                        ], spacing=20),
                        ft.Row([
                            ft.ElevatedButton(
                                text="Test Connessione",
                                icon=ft.Icons.WIFI_FIND,
                                bgcolor=ft.Colors.GREEN_600,
                                color=ft.Colors.WHITE,
                                on_click=test_llm_connection
                            )
                        ], spacing=20),

                        ft.Divider(),

                        ft.Text("⚙️ Impostazioni Monitoraggio", size=16, weight=ft.FontWeight.BOLD),
                        ft.Row([frequency_dropdown, enabled_switch], spacing=20),
                        keywords_field,

                        ft.Divider(),

                        ft.Text("📧 Notifiche", size=16, weight=ft.FontWeight.BOLD),
                        ft.Row([email_notifications_switch], spacing=20),
                        email_field,

                        ft.Divider(),

                        ft.Text("🌐 Siti Web Monitorati", size=16, weight=ft.FontWeight.BOLD),
                        websites_list,

                        ft.Container(
                            content=ft.ElevatedButton(
                                text="Gestisci Siti Web",
                                icon=ft.Icons.EDIT,
                                on_click=lambda _: self._show_websites_manager(current_config)
                            ),
                            alignment=ft.alignment.center
                        )
                    ], spacing=12, scroll=ft.ScrollMode.AUTO),
                    width=600,
                    height=500
                ),
                actions=[
                    ft.TextButton("Annulla", on_click=close_dialog),
                    ft.ElevatedButton(
                        text="Salva Configurazione",
                        icon=ft.Icons.SAVE,
                        bgcolor=ft.Colors.BLUE_600,
                        color=ft.Colors.WHITE,
                        on_click=save_config
                    )
                ]
            )

            self.app.page.overlay.append(dialog)
            dialog.open = True
            self.app.page.update()

        except Exception as e:
            logger.error(f"Error showing configuration dialog: {e}")
            self._show_error(f"Errore nell'apertura della configurazione: {e}")

    def _show_system_health_dialog(self):
        """Show system health monitoring dialog"""
        try:
            monitoring_service = self._get_monitoring_service()
            if not monitoring_service:
                self._show_error("Servizio di monitoraggio non disponibile")
                return

            # Get health and selector reports
            health_report = monitoring_service.run_health_check()
            selector_report = monitoring_service.run_selector_validation()

            # Check if enhanced features are available
            enhanced_available = health_report.get('individual_sites') is not None

            # Create health status cards
            health_cards = []

            if not enhanced_available:
                # Show message about enhanced features
                health_cards.append(
                    ft.Container(
                        content=ft.Column([
                            ft.Text("ℹ️ Funzionalità Avanzate Non Disponibili", size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.ORANGE_600),
                            ft.Text("Il monitoraggio avanzato della salute richiede il servizio di scraping potenziato.", size=12, color=ft.Colors.GREY_600),
                            ft.Text("Esegui: python scripts/setup_selenium.py per abilitare le funzionalità avanzate.", size=11, color=ft.Colors.BLUE_600),
                        ], spacing=4),
                        padding=ft.padding.all(16),
                        bgcolor=ft.Colors.ORANGE_50,
                        border_radius=8,
                        border=ft.border.all(1, ft.Colors.ORANGE_200)
                    )
                )

            if 'individual_sites' in health_report:
                for site_name, site_health in health_report['individual_sites'].items():
                    status_icon = "✅" if site_health['is_healthy'] else "❌"
                    status_color = ft.Colors.GREEN_600 if site_health['is_healthy'] else ft.Colors.RED_600

                    card = ft.Container(
                        content=ft.Column([
                            ft.Row([
                                ft.Text(f"{status_icon} {site_name}", size=14, weight=ft.FontWeight.BOLD),
                                ft.Container(expand=True),
                                ft.Text(f"{site_health['response_time']:.2f}s", size=12, color=ft.Colors.GREY_600)
                            ]),
                            ft.Text(f"Status: {site_health['status_code']}", size=12, color=ft.Colors.GREY_600),
                            ft.Text(f"Errori consecutivi: {site_health['consecutive_failures']}", size=12, color=ft.Colors.GREY_600) if site_health['consecutive_failures'] > 0 else ft.Container(),
                            ft.Text(f"Errore: {site_health['error_message']}", size=11, color=ft.Colors.RED_600) if site_health['error_message'] else ft.Container()
                        ], spacing=4),
                        padding=ft.padding.all(12),
                        bgcolor=ft.Colors.WHITE,
                        border_radius=8,
                        border=ft.border.all(1, status_color)
                    )
                    health_cards.append(card)

            # Create selector validation cards
            selector_cards = []

            if 'individual_sites' in selector_report:
                for site_name, site_validation in selector_report['individual_sites'].items():
                    working = site_validation['working_selectors']
                    total = site_validation['total_selectors']
                    success_rate = (working / max(total, 1)) * 100

                    status_color = ft.Colors.GREEN_600 if success_rate > 70 else ft.Colors.ORANGE_600 if success_rate > 40 else ft.Colors.RED_600

                    card = ft.Container(
                        content=ft.Column([
                            ft.Text(f"🎯 {site_name}", size=14, weight=ft.FontWeight.BOLD),
                            ft.Text(f"Selettori funzionanti: {working}/{total} ({success_rate:.1f}%)", size=12, color=status_color),
                            ft.Text(f"URL testato: {site_validation['url']}", size=10, color=ft.Colors.GREY_500)
                        ], spacing=4),
                        padding=ft.padding.all(12),
                        bgcolor=ft.Colors.WHITE,
                        border_radius=8,
                        border=ft.border.all(1, status_color)
                    )
                    selector_cards.append(card)

            # Overall statistics
            overall_health = health_report.get('overall_report', {})
            overall_selectors = selector_report.get('overall_report', {})

            stats_content = ft.Column([
                ft.Text("📊 Statistiche Generali", size=16, weight=ft.FontWeight.BOLD),
                ft.Row([
                    ft.Container(
                        content=ft.Column([
                            ft.Text("Salute Siti Web", size=12, weight=ft.FontWeight.BOLD),
                            ft.Text(f"{overall_health.get('healthy_sites', 0)}/{overall_health.get('total_sites', 0)} siti sani", size=14),
                            ft.Text(f"{overall_health.get('health_percentage', 0):.1f}% di salute", size=12, color=ft.Colors.GREY_600),
                            ft.Text(f"Tempo medio: {overall_health.get('average_response_time', 0):.2f}s", size=12, color=ft.Colors.GREY_600)
                        ], spacing=4),
                        padding=ft.padding.all(12),
                        bgcolor=ft.Colors.BLUE_50,
                        border_radius=8,
                        expand=True
                    ),
                    ft.Container(width=12),
                    ft.Container(
                        content=ft.Column([
                            ft.Text("Validazione Selettori", size=12, weight=ft.FontWeight.BOLD),
                            ft.Text(f"{overall_selectors.get('working_validations', 0)}/{overall_selectors.get('total_validations', 0)} funzionanti", size=14),
                            ft.Text(f"{overall_selectors.get('overall_success_rate', 0):.1f}% successo", size=12, color=ft.Colors.GREY_600)
                        ], spacing=4),
                        padding=ft.padding.all(12),
                        bgcolor=ft.Colors.GREEN_50,
                        border_radius=8,
                        expand=True
                    )
                ])
            ], spacing=12)

            def close_dialog(e):
                dialog.open = False
                self.app.page.update()

            def run_health_check(e):
                """Run new health check with visual feedback"""
                try:
                    self._show_loading("🏥 Esecuzione controllo salute del sistema...")
                    
                    # Close current dialog
                    dialog.open = False
                    self.app.page.update()
                    
                    # Run in background to avoid blocking UI
                    import threading
                    
                    def health_check_background():
                        try:
                            # Add a small delay to show the loading feedback
                            import time
                            time.sleep(0.5)
                            
                            self._close_loading_feedback()
                            self._show_system_health_dialog()
                            self._show_success("✅ Controllo salute completato")
                        except Exception as ex:
                            logger.error(f"Error in background health check: {ex}")
                            self._close_loading_feedback()
                            self._show_error(f"❌ Errore nel controllo salute: {ex}")
                    
                    thread = threading.Thread(target=health_check_background, daemon=True)
                    thread.start()
                    
                except Exception as ex:
                    logger.error(f"Error running health check: {ex}")
                    self._close_loading_feedback()
                    self._show_error(f"❌ Errore nel controllo salute: {ex}")

            # Create dialog content
            content_tabs = ft.Tabs(
                selected_index=0,
                tabs=[
                    ft.Tab(
                        text="Statistiche",
                        content=ft.Container(
                            content=stats_content,
                            padding=ft.padding.all(16)
                        )
                    ),
                    ft.Tab(
                        text="Salute Siti Web",
                        content=ft.Container(
                            content=ft.Column(health_cards, spacing=8, scroll=ft.ScrollMode.AUTO),
                            padding=ft.padding.all(16)
                        )
                    ),
                    ft.Tab(
                        text="Validazione Selettori",
                        content=ft.Container(
                            content=ft.Column(selector_cards, spacing=8, scroll=ft.ScrollMode.AUTO),
                            padding=ft.padding.all(16)
                        )
                    )
                ]
            )

            dialog = ft.AlertDialog(
                title=ft.Text("🏥 Stato di Salute del Sistema", weight=ft.FontWeight.BOLD),
                content=ft.Container(
                    content=content_tabs,
                    width=700,
                    height=500
                ),
                actions=[
                    ft.TextButton("Chiudi", on_click=close_dialog),
                    ft.ElevatedButton(
                        text="Aggiorna",
                        icon=ft.Icons.REFRESH,
                        bgcolor=ft.Colors.BLUE_600,
                        color=ft.Colors.WHITE,
                        on_click=run_health_check
                    )
                ]
            )

            self.app.page.overlay.append(dialog)
            dialog.open = True
            self.app.page.update()

        except Exception as e:
            logger.error(f"Error showing system health dialog: {e}")
            self._show_error(f"Errore nella visualizzazione dello stato sistema: {e}")

    def _load_incentives_config(self) -> dict:
        """Load incentives configuration from main settings"""
        try:
            # Try to load from main settings first
            if hasattr(self.app, 'settings_manager') and self.app.settings_manager:
                settings = self.app.settings_manager.settings
                if 'incentives' in settings:
                    logger.info("Loading incentives config from main settings")
                    return settings['incentives']

            # Fallback to separate config file for backward compatibility
            import json
            config_file = self.app.config.data_dir / 'incentives_config.json'

            if config_file.exists():
                logger.info("Loading incentives config from separate file")
                with open(config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                # Return default configuration
                logger.info("Using default incentives config")
                return {
                    'enabled': False,
                    'frequency': 'weekly',
                    'keywords': ['incentivi', 'finanziamenti', 'bandi', 'agevolazioni'],
                    'openrouter_api_key': '',
                    'llm_model': 'deepseek/deepseek-r1-0528-qwen3-8b:free',
                    'llm_api_url': 'https://openrouter.ai/api/v1',
                    'llm_enabled': False,
                    'email_notifications': True,
                    'notification_email': '',
                    'custom_models': {},
                    'websites': [
                        {
                            'name': 'MIMIT - Ministero Imprese',
                            'url': 'https://www.mimit.gov.it',
                            'enabled': True,
                            'search_paths': ['/it/incentivi-alle-imprese', '/it/notizie', '/it/bandi-e-avvisi']
                        },
                        {
                            'name': 'Invitalia',
                            'url': 'https://www.invitalia.it',
                            'enabled': True,
                            'search_paths': ['/cosa-facciamo/sosteniamo-le-imprese', '/news-e-media/news', '/bandi-e-gare']
                        },
                        {
                            'name': 'SIMEST',
                            'url': 'https://www.simest.it',
                            'enabled': True,
                            'search_paths': ['/servizi', '/news', '/bandi']
                        }
                    ]
                }
        except Exception as e:
            logger.error(f"Error loading incentives config: {e}")
            return {}

    def _save_incentives_config(self, config: dict):
        """Save incentives configuration to main settings and backup file"""
        try:
            import json
            from pathlib import Path

            # Save to main settings first
            if hasattr(self.app, 'settings_manager') and self.app.settings_manager:
                logger.info("Saving incentives config to main settings")
                self.app.settings_manager.settings['incentives'] = config
                self.app.settings_manager._save_settings()
                logger.info("Incentives config saved to main settings successfully")

            # Also save to separate file for backward compatibility
            config_file = Path("data/incentives_config.json")
            config_file.parent.mkdir(parents=True, exist_ok=True)

            # Debug logging
            websites_count = len(config.get('websites', []))
            logger.info(f"Saving config with {websites_count} websites to {config_file.absolute()}")

            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)

            # Verify the save worked
            with open(config_file, 'r', encoding='utf-8') as f:
                saved_config = json.load(f)
                saved_websites_count = len(saved_config.get('websites', []))
                logger.info(f"Verified save: {saved_websites_count} websites in saved file")

            logger.info(f"Incentives configuration saved successfully to both main settings and {config_file}")

        except Exception as e:
            logger.error(f"Error saving incentives config: {e}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            raise

    def _create_websites_list(self, websites: list) -> ft.Container:
        """Create a list display of monitored websites"""
        website_items = []

        for website in websites:
            website_items.append(
                ft.ListTile(
                    leading=ft.Icon(
                        ft.Icons.LANGUAGE,
                        color=ft.Colors.GREEN_600 if website.get('enabled', True) else ft.Colors.GREY_400
                    ),
                    title=ft.Text(website.get('name', 'Sito Sconosciuto')),
                    subtitle=ft.Text(website.get('url', '')),
                    trailing=ft.Icon(
                        ft.Icons.CHECK_CIRCLE if website.get('enabled', True) else ft.Icons.CANCEL,
                        color=ft.Colors.GREEN_600 if website.get('enabled', True) else ft.Colors.GREY_400
                    )
                )
            )

        return ft.Container(
            content=ft.Column(website_items, spacing=4),
            height=150,
            border=ft.border.all(1, ft.Colors.GREY_300),
            border_radius=8,
            padding=ft.padding.all(8)
        )

    def _show_websites_manager(self, current_config: dict):
        """Show websites management dialog"""
        try:
            websites = current_config.get('websites', []).copy()

            # Create websites list container first
            websites_column = ft.Column([], spacing=8, scroll=ft.ScrollMode.AUTO)

            # Create a container for the counter that we can update
            counter_text = ft.Text(f"Siti configurati: {len(websites)}", size=12, color=ft.Colors.GREY_600)

            # Create status text for auto-save feedback
            status_text = ft.Text("💾 Salvataggio automatico attivo", size=11, color=ft.Colors.GREEN_600, italic=True)



            def rebuild_dialog_content():
                """Rebuild the entire dialog content"""
                try:
                    # Clear and rebuild the websites list
                    websites_column.controls.clear()

                    for i, website in enumerate(websites):
                        websites_column.controls.append(
                            ft.Card(
                                content=ft.ListTile(
                                    leading=ft.Icon(
                                        ft.Icons.LANGUAGE,
                                        color=ft.Colors.GREEN_600 if website.get('enabled', True) else ft.Colors.GREY_400
                                    ),
                                    title=ft.Text(website.get('name', 'Sito Sconosciuto')),
                                    subtitle=ft.Text(website.get('url', '')),
                                    trailing=ft.Row([
                                        ft.IconButton(
                                            icon=ft.Icons.EDIT,
                                            tooltip="Modifica",
                                            on_click=lambda _, idx=i: edit_website(idx)
                                        ),
                                        ft.IconButton(
                                            icon=ft.Icons.DELETE,
                                            tooltip="Elimina",
                                            icon_color=ft.Colors.RED_600,
                                            on_click=lambda _, idx=i: remove_website(idx)
                                        )
                                    ], tight=True)
                                )
                            )
                        )

                    # Update counter
                    counter_text.value = f"Siti configurati: {len(websites)}"

                    # Update page
                    self.app.page.update()

                except Exception as ex:
                    logger.error(f"Error rebuilding dialog content: {ex}")

            def save_changes():
                """Save current websites configuration to file"""
                try:
                    logger.info(f"save_changes called: updating config with {len(websites)} websites")
                    logger.info(f"Current websites: {[w.get('name', 'Unknown') for w in websites]}")

                    current_config['websites'] = websites
                    self._save_incentives_config(current_config)

                    # Update status text to show save confirmation
                    from datetime import datetime
                    now = datetime.now().strftime("%H:%M:%S")
                    status_text.value = f"✅ Salvato automaticamente alle {now}"
                    status_text.color = ft.Colors.GREEN_600

                    logger.info(f"Auto-saved {len(websites)} websites to configuration file")
                except Exception as ex:
                    logger.error(f"Error auto-saving websites: {ex}")
                    import traceback
                    logger.error(f"Full traceback: {traceback.format_exc()}")
                    status_text.value = f"❌ Errore nel salvataggio: {ex}"
                    status_text.color = ft.Colors.RED_600

            def add_website(e):
                """Add new website"""
                try:
                    websites.append({
                        'name': 'Nuovo Sito',
                        'url': 'https://example.com',
                        'enabled': True,
                        'search_paths': ['/']
                    })
                    rebuild_dialog_content()
                    save_changes()  # Auto-save
                    logger.info(f"Added new website. Total websites: {len(websites)}")
                    self._show_success("✅ Nuovo sito aggiunto e salvato!")
                except Exception as ex:
                    logger.error(f"Error adding website: {ex}")
                    self._show_error(f"Errore nell'aggiunta del sito: {ex}")

            def remove_website(index):
                """Remove website"""
                try:
                    if 0 <= index < len(websites):
                        removed = websites.pop(index)
                        rebuild_dialog_content()
                        save_changes()  # Auto-save
                        logger.info(f"Removed website: {removed.get('name', 'Unknown')}")
                        self._show_success(f"✅ Sito '{removed.get('name', 'Unknown')}' rimosso e salvato!")
                except Exception as ex:
                    logger.error(f"Error removing website: {ex}")
                    self._show_error(f"Errore nella rimozione del sito: {ex}")

            def edit_website(index):
                """Edit website"""
                try:
                    if 0 <= index < len(websites):
                        self._show_website_edit_dialog(websites[index], lambda updated: update_website(index, updated))
                except Exception as ex:
                    logger.error(f"Error editing website: {ex}")
                    self._show_error(f"Errore nella modifica del sito: {ex}")

            def update_website(index, updated_website):
                """Update website"""
                try:
                    if 0 <= index < len(websites):
                        websites[index] = updated_website
                        rebuild_dialog_content()
                        save_changes()  # Auto-save
                        logger.info(f"Updated website: {updated_website.get('name', 'Unknown')}")
                        self._show_success(f"✅ Sito '{updated_website.get('name', 'Unknown')}' aggiornato e salvato!")
                except Exception as ex:
                    logger.error(f"Error updating website: {ex}")
                    self._show_error(f"Errore nell'aggiornamento del sito: {ex}")

            def save_websites(e):
                """Save websites configuration and close dialog"""
                try:
                    # Ensure final save (though auto-save should have already saved)
                    current_config['websites'] = websites
                    self._save_incentives_config(current_config)
                    manager_dialog.open = False
                    self.app.page.update()
                    self._show_success("✅ Gestione siti web completata!")
                    logger.info(f"Final save: {len(websites)} websites in configuration")
                except Exception as ex:
                    logger.error(f"Error in final save: {ex}")
                    self._show_error(f"Errore nel salvataggio finale: {ex}")

            def close_manager(e):
                """Close manager"""
                try:
                    manager_dialog.open = False
                    self.app.page.update()
                except Exception as ex:
                    logger.error(f"Error closing manager: {ex}")

            # Create manager dialog
            manager_dialog = ft.AlertDialog(
                title=ft.Text("🌐 Gestione Siti Web", weight=ft.FontWeight.BOLD),
                content=ft.Container(
                    content=ft.Column([
                        ft.Row([
                            ft.ElevatedButton(
                                text="Aggiungi Sito",
                                icon=ft.Icons.ADD,
                                on_click=add_website,
                                bgcolor=ft.Colors.GREEN_600,
                                color=ft.Colors.WHITE
                            ),
                            ft.Column([
                                counter_text,
                                status_text
                            ], spacing=2, horizontal_alignment=ft.CrossAxisAlignment.END)
                        ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                        ft.Divider(),
                        ft.Container(
                            content=websites_column,
                            height=300,
                            border=ft.border.all(1, ft.Colors.GREY_300),
                            border_radius=8,
                            padding=ft.padding.all(8)
                        )
                    ], spacing=12),
                    width=600,
                    height=450
                ),
                actions=[
                    ft.TextButton("Annulla", on_click=close_manager),
                    ft.ElevatedButton(
                        text="Chiudi",
                        icon=ft.Icons.CHECK,
                        bgcolor=ft.Colors.GREEN_600,
                        color=ft.Colors.WHITE,
                        on_click=save_websites
                    )
                ]
            )

            # Add dialog to page first
            self.app.page.overlay.append(manager_dialog)

            # Initialize list after dialog is added to page
            rebuild_dialog_content()

            # Open dialog
            manager_dialog.open = True
            self.app.page.update()

            logger.info(f"Opened websites manager with {len(websites)} websites")

        except Exception as e:
            logger.error(f"Error showing websites manager: {e}")
            self._show_error(f"Errore nel gestore siti web: {e}")

    def _show_website_edit_dialog(self, website: dict, on_save_callback):
        """Show website edit dialog"""
        try:
            name_field = ft.TextField(
                label="Nome Sito",
                value=website.get('name', ''),
                width=300
            )

            url_field = ft.TextField(
                label="URL Base",
                value=website.get('url', ''),
                width=300
            )

            enabled_switch = ft.Switch(
                label="Attivo",
                value=website.get('enabled', True)
            )

            paths_field = ft.TextField(
                label="Percorsi di Ricerca (uno per riga)",
                value='\n'.join(website.get('search_paths', ['/'])),
                multiline=True,
                min_lines=3,
                max_lines=6,
                width=300
            )

            def save_website(e):
                """Save website"""
                try:
                    updated_website = {
                        'name': name_field.value,
                        'url': url_field.value,
                        'enabled': enabled_switch.value,
                        'search_paths': [path.strip() for path in paths_field.value.split('\n') if path.strip()]
                    }

                    on_save_callback(updated_website)
                    edit_dialog.open = False
                    self.app.page.update()

                except Exception as ex:
                    logger.error(f"Error saving website: {ex}")
                    self._show_error(f"Errore nel salvataggio: {ex}")

            def close_edit(e):
                """Close edit dialog"""
                edit_dialog.open = False
                self.app.page.update()

            edit_dialog = ft.AlertDialog(
                title=ft.Text("✏️ Modifica Sito Web", weight=ft.FontWeight.BOLD),
                content=ft.Container(
                    content=ft.Column([
                        name_field,
                        url_field,
                        enabled_switch,
                        paths_field
                    ], spacing=12),
                    width=400
                ),
                actions=[
                    ft.TextButton("Annulla", on_click=close_edit),
                    ft.ElevatedButton(
                        text="Salva",
                        icon=ft.Icons.SAVE,
                        bgcolor=ft.Colors.BLUE_600,
                        color=ft.Colors.WHITE,
                        on_click=save_website
                    )
                ]
            )

            self.app.page.overlay.append(edit_dialog)
            edit_dialog.open = True
            self.app.page.update()

        except Exception as e:
            logger.error(f"Error showing website edit dialog: {e}")
            self._show_error(f"Errore nella modifica sito web: {e}")

    def _get_available_models(self, config: dict) -> dict:
        """Get available LLM models (default + custom)"""
        # Default models
        default_models = {
            'gpt-4': 'GPT-4 (OpenAI)',
            'gpt-4-turbo': 'GPT-4 Turbo (OpenAI)',
            'gpt-3.5-turbo': 'GPT-3.5 Turbo (OpenAI)',
            'claude-3-opus': 'Claude 3 Opus (Anthropic)',
            'claude-3-sonnet': 'Claude 3 Sonnet (Anthropic)',
            'claude-3-haiku': 'Claude 3 Haiku (Anthropic)',
            'llama-2-70b': 'Llama 2 70B (Meta)',
            'mixtral-8x7b': 'Mixtral 8x7B (Mistral)',
            'gemini-pro': 'Gemini Pro (Google)',
            'command-r-plus': 'Command R+ (Cohere)'
        }

        # Add custom models
        custom_models = config.get('custom_models', {})

        # Merge and return
        all_models = {**default_models, **custom_models}
        return all_models

    def _show_models_manager(self, current_config: dict):
        """Show models management dialog"""
        try:
            custom_models = current_config.get('custom_models', {}).copy()

            def add_model(e):
                """Add new custom model"""
                model_id = model_id_field.value.strip()
                model_name = model_name_field.value.strip()

                if not model_id or not model_name:
                    self._show_error("Inserisci sia ID che nome del modello")
                    return

                custom_models[model_id] = model_name
                model_id_field.value = ""
                model_name_field.value = ""
                refresh_models_list()
                models_dialog.content.content.update()
                self.app.page.update()

            def remove_model(model_id):
                """Remove custom model"""
                if model_id in custom_models:
                    del custom_models[model_id]
                    refresh_models_list()
                    models_dialog.content.content.update()
                    self.app.page.update()

            def refresh_models_list():
                """Refresh the models list"""
                models_column.controls.clear()

                # Show default models (read-only)
                default_models = self._get_available_models({})
                models_column.controls.append(
                    ft.Text("🔧 Modelli Predefiniti", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_600)
                )

                for model_id, model_name in default_models.items():
                    if model_id not in custom_models:  # Don't show if overridden
                        models_column.controls.append(
                            ft.Card(
                                content=ft.ListTile(
                                    leading=ft.Icon(ft.Icons.SMART_TOY, color=ft.Colors.BLUE_600),
                                    title=ft.Text(model_name),
                                    subtitle=ft.Text(model_id, size=12, color=ft.Colors.GREY_600),
                                    trailing=ft.Icon(ft.Icons.LOCK, color=ft.Colors.GREY_400, tooltip="Modello predefinito")
                                )
                            )
                        )

                # Show custom models (editable)
                if custom_models:
                    models_column.controls.append(ft.Container(height=10))
                    models_column.controls.append(
                        ft.Text("⚙️ Modelli Personalizzati", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN_600)
                    )

                    for model_id, model_name in custom_models.items():
                        models_column.controls.append(
                            ft.Card(
                                content=ft.ListTile(
                                    leading=ft.Icon(ft.Icons.EDIT, color=ft.Colors.GREEN_600),
                                    title=ft.Text(model_name),
                                    subtitle=ft.Text(model_id, size=12, color=ft.Colors.GREY_600),
                                    trailing=ft.IconButton(
                                        icon=ft.Icons.DELETE,
                                        tooltip="Elimina modello personalizzato",
                                        icon_color=ft.Colors.RED_600,
                                        on_click=lambda _, mid=model_id: remove_model(mid)
                                    )
                                )
                            )
                        )

            def save_models(e):
                """Save custom models"""
                current_config['custom_models'] = custom_models
                self._save_incentives_config(current_config)
                models_dialog.open = False
                self.app.page.update()
                self._show_success("✅ Modelli personalizzati salvati!")

            def close_models_manager(e):
                """Close models manager"""
                models_dialog.open = False
                self.app.page.update()

            # Create form fields
            model_id_field = ft.TextField(
                label="ID Modello",
                hint_text="es: gpt-4-custom, claude-3-custom",
                width=250
            )

            model_name_field = ft.TextField(
                label="Nome Visualizzato",
                hint_text="es: GPT-4 Custom, Claude 3 Custom",
                width=250
            )

            # Create models list
            models_column = ft.Column([], spacing=8, scroll=ft.ScrollMode.AUTO)

            # Create models manager dialog
            models_dialog = ft.AlertDialog(
                title=ft.Text("🤖 Gestione Modelli LLM", weight=ft.FontWeight.BOLD),
                content=ft.Container(
                    content=ft.Column([
                        ft.Text("➕ Aggiungi Modello Personalizzato", size=14, weight=ft.FontWeight.BOLD),
                        ft.Row([model_id_field, model_name_field], spacing=12),
                        ft.Row([
                            ft.ElevatedButton(
                                text="Aggiungi Modello",
                                icon=ft.Icons.ADD,
                                on_click=add_model
                            )
                        ]),

                        ft.Divider(),

                        ft.Text("📋 Modelli Disponibili", size=14, weight=ft.FontWeight.BOLD),
                        ft.Container(
                            content=models_column,
                            height=300,
                            border=ft.border.all(1, ft.Colors.GREY_300),
                            border_radius=8,
                            padding=ft.padding.all(8)
                        ),

                        ft.Container(
                            content=ft.Text(
                                "💡 Suggerimento: Puoi aggiungere qualsiasi modello supportato da OpenRouter. "
                                "Controlla la documentazione di OpenRouter per gli ID modello corretti.",
                                size=12,
                                color=ft.Colors.GREY_600,
                                italic=True
                            ),
                            padding=ft.padding.all(8),
                            bgcolor=ft.Colors.BLUE_50,
                            border_radius=8
                        )
                    ], spacing=12),
                    width=600,
                    height=500
                ),
                actions=[
                    ft.TextButton("Annulla", on_click=close_models_manager),
                    ft.ElevatedButton(
                        text="Salva Modelli",
                        icon=ft.Icons.SAVE,
                        bgcolor=ft.Colors.BLUE_600,
                        color=ft.Colors.WHITE,
                        on_click=save_models
                    )
                ]
            )

            # Initialize list
            refresh_models_list()

            self.app.page.overlay.append(models_dialog)
            models_dialog.open = True
            self.app.page.update()

        except Exception as e:
            logger.error(f"Error showing models manager: {e}")
            self._show_error(f"Errore nel gestore modelli: {e}")
